console.log('LOAD AJAX_DOCTOR.JS');
console.log(window.location.search);

// (function(g,e,o,t,a,r,ge,tl,y){
// t=g.getElementsByTagName(e)[0];y=g.createElement(e);y.async=true;
// y.src='https://g1386590346.co/gl?id=-NngMPE0KhuUX1PQ11Be&refurl='+g.referrer+'&winurl='+encodeURIComponent(window.location);
// t.parentNode.insertBefore(y,t);
// })(document,'script');

var jtemplat = "";
var jtemplng = "";
var jtempgeolocation = "";
var jgeotargetlycount = 0;


document.addEventListener( 'DOMContentLoaded', function($) {

    // var data_order = 'DESC';

    // if ( 'yes' === settings.ascending_order ) {
    //     data_order = 'ASC'
    // } else {
    //     data_order = 'DESC'
    // }

    // // Define the AJAX action and any additional data to send
    // var data = {
    //     'action': 'custom_ajax_specialty_action', // Replace with your own action name
    //     'post_type': 'specialty', // Specify the custom post type name
    //     'order': data_order,
    // };

    // console.log('data_order from JS: ', data_order);

    // // Make the AJAX request
    // // $.post(ajax_params.ajax_url, data, function(response) {
    // $.post('/wp-admin/admin-ajax.php', data, function(response) {
    //     // 'response' will contain the data returned from the server
    //     console.log('response from JS: ', response);
    //     // Process the 'hospital' post data as needed
    // });
});

let func1Called = false;
let func2Called = false;
let intervalId;
let geotargetlyActive = false;

(function($) {
    function calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371; // Radius of the earth in km
        const dLat = deg2rad(lat2 - lat1);
        const dLon = deg2rad(lon2 - lon1);
        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distance = R * c; // Distance in km
        return distance;
    }

    function deg2rad(deg) {
        return deg * (Math.PI/180);
    }

    

    $(function() {
        //console.log("jtest doctor script222");
        //console.log($("#specialty-list").val());
        //console.log(customData.hospitaldata);

        //geotargetly_loaded(); // gets called when data is available

        function getLocation() {
            if (navigator.geolocation) {
                jtempgeolocation = 1;
                navigator.geolocation.getCurrentPosition(showPosition, handleGeolocationError);
            } else {
                geotargetlyActive = true;
                
                jtempgeolocation = 0;
                //console.log("Geolocation is not supported by this browser.");
                // Consider calling loadGeotargetly() here as a fallback for non-supported browsers
                var cookieName = "geostatus";
                if (isCookieExpired(cookieName)) {
                    // Load external JS
                    loadGeotargetly();
                }else{
                    jmanualgeotargetly();
                }
            }
        }

        function showPosition(position) {
            console.log("Geolocation permission was successful");
            geotargetlyActive = true;
            jtemplat = position.coords.latitude;
            jtemplng = position.coords.longitude;
            jmanualgeotargetly();
        }

        function handleGeolocationError(error) {
            if (error.code === error.PERMISSION_DENIED) {
                console.log("Geolocation permission was denied");
                var cookieName = "geostatus";
                if (isCookieExpired(cookieName)) {
                    console.log('geotargetly cookies not working');
                    // Load external JS
                    geotargetlyActive = true;
                    loadGeotargetly();
                }else{
                    console.log('geotargetly cookies working');
                    geotargetlyActive = true;
                    jtemplat = getCookie("geolat");
                    jtemplng = getCookie("geolng");
                    jmanualgeotargetly();
                }

            }
        }

        function loadGeotargetly() {
            var script = document.createElement('script');
            script.async = true;
            script.src = 'https://g1386590346.co/gl?id=-NngMPE0KhuUX1PQ11Be&refurl='+document.referrer+'&winurl='+encodeURIComponent(window.location);
            document.body.appendChild(script);

            script.onload = function() {
                console.log("Geotargetly loaded.");
                // Setup a callback or interval to check if the Geotargetly data is available
                waitForGeotargetlyData();
            };
        }

        function waitForGeotargetlyData() {
            if (typeof geotargetly_lat === "function" && typeof geotargetly_lng === "function") {
                jtemplat = geotargetly_lat(); // User's latitude
                jtemplng = geotargetly_lng(); // User's longitude

                setCookie("geostatus", "1", 60);
                setCookie("geolat", jtemplat, 60);
                setCookie("geolng", jtemplng, 60);
                
                // Check if Geotargetly functions are available and then display the data
                jmanualgeotargetly();
            } else {
                // If not ready, check again after a delay
                jgeotargetlycount += 1;
                if(jgeotargetlycount < 10){
                    setTimeout(waitForGeotargetlyData, 1000);
                }else{
                    jtemplat = 3.0473; // User's latitude
                    jtemplng = 101.5051; // User's longitude
                    jmanualgeotargetly();
                }
                
            }
        }

        // window.onload = getLocation;
        getLocation();

        // Function to set a cookie with a specified name, value, and expiration time in minutes
        function setCookie(name, value, minutes) {
            var expires = "";
            if (minutes) {
                var date = new Date();
                date.setTime(date.getTime() + (minutes * 60 * 1000));
                expires = "; expires=" + date.toUTCString();
            }
            document.cookie = name + "=" + value + expires + "; path=/";
        }

        // Function to retrieve the value of a cookie by its name
        function getCookie(name) {
            var nameEQ = name + "=";
            var cookies = document.cookie.split(';');
            for (var i = 0; i < cookies.length; i++) {
                var cookie = cookies[i];
                while (cookie.charAt(0) === ' ') {
                    cookie = cookie.substring(1, cookie.length);
                }
                if (cookie.indexOf(nameEQ) === 0) {
                    return cookie.substring(nameEQ.length, cookie.length);
                }
            }
            return null;
        }

        // Function to check if a cookie has expired
        function isCookieExpired(name) {
            var cookie = getCookie(name);
            if (cookie) {
                var expiration = cookie.split('=')[1];
                var expirationTime = new Date(expiration).getTime();
                var currentTime = new Date().getTime();
                return expirationTime < currentTime;
            }
            return true; // Cookie does not exist or has expired
        }



        // // Set a timeout for 5 seconds
        // const timeoutId = setTimeout(() => {
        //     // Clear the interval
        //     console.log("clear interval intervalId");
        //     clearInterval(intervalId);

        //     // Check if func1 has been called
        //     if (!func1Called) {
        //         // Manually call func2
        //         jmanualgeotargetly();
        //     }
        // }, 5000);

        // // Set an interval to check every second
        // intervalId = setInterval(() => {
        //     // Check if func1 has been called
        //     console.log("geotargetly 1st check");
        //     if (func1Called) {
        //         //console.log("geotargetly 1st check");
        //         // Clear the timeout as func1 has been called
        //         clearTimeout(timeoutId);
        //         clearInterval(intervalId);
        //     }
        // }, 1000);

        function jmanualgeotargetly() {
            console.log('jmanualgeotargetly called');
            func2Called = true;
            // Add your func2 logic here
            
            var userLat = jtemplat; // User's latitude
            var userLon = jtemplng; // User's longitude

            console.log('geolocation lat lng: ', userLat, userLon);

            console.log('customhospital: ', customhospital);
            console.log('customData: ', customData);
            // console.log('ajax_params: ', ajax_params);

            //start geotargetly dependancy function
                var hospitaldata = customData.hospitaldata;

                // Check if hospitaldata exists and is an array
                if (!hospitaldata || !Array.isArray(hospitaldata) || hospitaldata.length === 0) {
                    console.error('Hospital data is not available or empty');
                    return;
                }

                var removeddata;
                for (let i = 0; i < hospitaldata.length; i++) {
                    // Check if hospital data and acfdata exist
                    if (hospitaldata[i] && hospitaldata[i].acfdata && hospitaldata[i].acfdata.lat && hospitaldata[i].acfdata.lng) {
                        hospitaldata[i].distance = calculateDistance(userLat, userLon, hospitaldata[i].acfdata.lat, hospitaldata[i].acfdata.lng);
                        //console.log(hospitaldata[i].post_title + " - " + hospitaldata[i].distance);
                    } else {
                        console.warn('Missing location data for hospital at index', i);
                        hospitaldata[i].distance = 999999; // Set a high distance for hospitals without location data
                    }
                }

                //console.log('geotargetly_loaded????');

                hospitaldata.sort((a, b) => a.distance - b.distance);

                // Check if first hospital exists and has post_title before accessing it
                if(hospitaldata.length > 0 && hospitaldata[0] && hospitaldata[0].post_title == "Shah Alam"){
                    hospitaldata.shift();
                }

                for (let i = 0; i < hospitaldata.length; i++) {
                
                    console.log(hospitaldata[i].post_title + " - " + hospitaldata[i].distance);
                    $("#jgeolocation").append("<br/> <span>" + hospitaldata[i].post_title + " - " + hospitaldata[i].distance + "</span>");
                }

                $( document ).ready(function() {
                    if($(".jteHospitalFooterContainer").length){
                        if($("#jte_hospital_footer").length && hospitaldata.length && hospitaldata[0] && hospitaldata[0].post_title && $("body").hasClass("home")){
                            var str = hospitaldata[0].post_title;
                            str = str.replace(/\s+/g, '-').toLowerCase();

                            // $("#jte_hospital_footer").val(str);
                            // $("#jte_hospital_footer").trigger('change');

                            $("#jte_hospital_footer").val(str).selectmenu("refresh").trigger('selectmenuchange');
                            // $("#jte_hospital_footer").trigger('selectmenuchange');

                            console.log('str:', str);
                        }
                    }
                });

                if($('.specialtiesSelectField').length){
                    if(hospitaldata.length > 0 && hospitaldata[0] && hospitaldata[0].ID && hospitaldata[0].post_title) {
                        console.log('HAVE Gravity Form!!', hospitaldata[0].ID, hospitaldata[0]);
                        $(".specialtiesSelectField select option").each(function(){
                            if($(this).text() === hospitaldata[0].post_title){
                                console.log('MATCHED Val');
                                $(".specialtiesSelectField select").val(hospitaldata[0].post_title).change();
                            } else {
                                console.log('NOT MATCHED Val');
                            }
                        })
                    } else {
                        console.warn('Hospital data not available for specialties select field');
                    }
                }

                if($(".jteHPcarouselContainer").length){
                    var currentURL = window.location.href;
                    var jcheckhospital = false;
                    if (currentURL.indexOf("/hospital/") !== -1) {
                        jcheckhospital = true;
                    }
                    if (currentURL.indexOf("/hospitals/") !== -1) {
                        jcheckhospital = true;
                    }
                    if($("#jte_hospital").length && hospitaldata.length && hospitaldata[0] && hospitaldata[0].ID && jcheckhospital == false){
                        //var str = hospitaldata[0].post_title;
                        //str = str.replace(/\s+/g, '-').toLowerCase();

                        // $("#jte_hospital").val(hospitaldata[0].ID);
                        // $("#jte_hospital").trigger('change');

                        $("#jte_hospital").val(hospitaldata[0].ID).selectmenu("refresh").trigger('selectmenuchange');
                        // $("#jte_hospital").trigger('selectmenuchange');
                    }
                }

                if($(".jteHPcarouselContainer02").length){
                    var currentURL = window.location.href;
                    var jcheckhospital = false;
                    if (currentURL.indexOf("/hospital/") !== -1) {
                        jcheckhospital = true;
                    }
                    if (currentURL.indexOf("/hospitals/") !== -1) {
                        jcheckhospital = true;
                    }

                    console.log('geotargetlyActive: ', geotargetlyActive, jcheckhospital);
                    if($("#jte_hospital02").length && hospitaldata.length && hospitaldata[0] && hospitaldata[0].ID && jcheckhospital == false && geotargetlyActive == true){
                        //var str = hospitaldata[0].post_title;
                        //str = str.replace(/\s+/g, '-').toLowerCase();

                        // $("#jte_hospital02").val(hospitaldata[0].ID);
                        // $("#jte_hospital02").trigger('change');

                        console.log('#jte_hospital02 TRIGGER', hospitaldata, hospitaldata.length, hospitaldata[0].ID);
                        var tempcheck = 0;
                        var tempcount = 0;

                        // custom code to settle triston problem caused by CAH
                        // while (tempcheck === 0) {
                        //     $("#jte_hospital02 option").each(function() {
                        //         if ($(this).val() == hospitaldata[tempcount].ID) {
                        //             console.log("Found an option with the value: " + hospitaldata[tempcount].ID);
                        //             $("#jte_hospital02").val(hospitaldata[tempcount].ID).selectmenu("refresh").trigger('selectmenuchange');
                        //             tempcheck = 1;
                        //         } else {
                        //             console.log($(this).val() + " ||| " + hospitaldata[tempcount].ID + " ||| " + 'not matched!!');
                        //         }
                        //     });
                        //     // Example:
                        //     tempcount++;

                        //     // Check condition to stop loop
                        //     if (tempcheck === 1) {
                        //         break; // Exit the loop
                        //     }
                        // }

                        $("#jte_hospital02 option").each(function() {
                            if ($(this).val() == hospitaldata[0].ID) {
                                console.log("Found an option with the value: " + hospitaldata[0].ID);
                                $("#jte_hospital02").val(hospitaldata[0].ID).selectmenu("refresh").trigger('selectmenuchange');
                            } else {
                                console.log($(this).val() + " ||| " + hospitaldata[0].ID + " ||| " + 'not matched!!');
                            }
                        });

                        
                        // if(selectedValue === hospitaldata[0].ID) {
                        //     console.log("The selected value is equal to myValue.");
                        //     $("#jte_hospital02").val(hospitaldata[0].ID).selectmenu("refresh").trigger('selectmenuchange');
                        // } else {
                        //     console.log("The selected value is not equal to myValue.");
                        // }

                        // $("#jte_hospital02").val(hospitaldata[0].ID).selectmenu("refresh").trigger('selectmenuchange');


                        // $("#jte_hospital02").trigger('selectmenuchange');
                        // console.log('TRIGGER Selectmenu');
                        // $(document).on('selectmenuInitialized', function() {
                        //     console.log('SUCCESS TRIGGER Selectmenu01');
                        //     $("#jte_hospital02").val(hospitaldata[0].ID).selectmenu("refresh");
                        //     console.log('SUCCESS TRIGGER Selectmenu');
                        // });
                    }
                }

                if($(".jteTopNavEmergency").length){
                    //console.log("got emergency div");
                    if($(".jteTopNavEmergency a.elementor-button-link").length){
                        $(".jpopup-content .jemergencycontentmain").html("");
                        $(".jpopup-content .jemergencycontentsub").html("");
                        //console.log("got emergency22 div");
                        $(".jteTopNavEmergency .elementor-button-link").unbind();
                        //appends an "active" class to .popup and .popup-content when the "Open" button is clicked
                        $(".jteTopNavEmergency .elementor-button-link").on("click", function(event) {
                            //console.log("emergency is clicked");
                            //event.stopPropagation();
                            event.preventDefault();

                            $('body').addClass('noScroll');
                            $(".jpopup-overlay, .jpopup-content").addClass("active");
                            $(".jpopup-overlay").fadeIn(300);
                        });

                        $('.cahEmergencyBtn a').on("click", function(event) {
                            console.log("emergency is clicked");
                            //event.stopPropagation();
                            event.preventDefault();

                            $('body').addClass('noScroll');
                            $(".jpopup-overlay, .jpopup-content").addClass("active");
                            $(".jpopup-overlay").fadeIn(300);
                        });

                        $('.jemergencycontentdivider a').on("click", function(event) {
                            $(this).hide();
                            $('.jemergencycontentsub').fadeIn(300);
                            $('.jpopup-content').addClass('show');
                        });

                        //removes the "active" class to .popup and .popup-content when the "Close" button is clicked 
                        $(".jpopup-close, .jpopup-overlay").on("click", function() {
                            $(".jpopup-overlay, .jpopup-content").removeClass("active");
                            $(".jpopup-overlay").fadeOut(300);
                            $('body').removeClass('noScroll');
                        });

                        $('.jpopup-content').click(function(e){
                            e.stopPropagation();
                            //console.log('jpopup-content CLICKED');
                        })

                        var divhtml = '';

                        console.log('hospitaldata: ', hospitaldata);

                        // Check if first hospital data exists before using it
                        if(hospitaldata.length > 0 && hospitaldata[0] && hospitaldata[0].post_title && hospitaldata[0].acfdata) {
                            divhtml = `<div class="jemergencycontent">
                                            <span class="jemergencyhospital">${hospitaldata[0].post_title}</span>
                                            <span class="jemergencymobile"><a href="tel:${hospitaldata[0].acfdata.emergency_room_phone_line}">${hospitaldata[0].acfdata.emergency_room_phone_line_display_only}</a></span>
                                            <div class="jemergencyhospitalright">

                                                <div class="jemergencyhospitalrightSocial">
                                                    <span class="jemergencylogowaze"><a href="${hospitaldata[0].acfdata.waze_link}" target="_blank">waze</a></span>
                                                    <span class="jemergencylogogoogle"><a href="${hospitaldata[0].acfdata.google_map_link}" target="_blank">google</a></span>
                                                </div>
                                            </div>
                                        </div>`;

                            $(".jpopup-content .jemergencycontentmain").append(divhtml);
                        } else {
                            console.warn('Hospital data not available for emergency content');
                        }

                        function sortExcludingFirst(dataArray) {
                            const firstElement = dataArray.slice(0, 1);
                            const sortedArray = dataArray.slice(1).sort((a, b) => {
                                return a.post_title.localeCompare(b.post_title);
                            });
                            return firstElement.concat(sortedArray);
                        }
                        var hospitaldataFinal = sortExcludingFirst(hospitaldata);
                        console.log('hospitaldataFinal: ', hospitaldataFinal);

                        for (let i = 1; i < hospitaldata.length; i++) {
                
                            var divhtmlsub = '';

                            divhtmlsub = `<div class="jemergencycontent">
                                            <span class="jemergencyhospital">${hospitaldataFinal[i].post_title}</span>
                                            <span class="jemergencymobile"><a href="tel:${hospitaldataFinal[i].acfdata.emergency_room_phone_line}">${hospitaldataFinal[i].acfdata.emergency_room_phone_line_display_only}</a></span>
                                            <div class="jemergencyhospitalright">
                                                
                                                <div class="jemergencyhospitalrightSocial">
                                                    <span class="jemergencylogowaze"><a href="${hospitaldataFinal[i].acfdata.waze_link}" target="_blank">waze</a></span>
                                                    <span class="jemergencylogogoogle"><a href="${hospitaldataFinal[i].acfdata.google_map_link}" target="_blank">google</a></span>
                                                </div>
                                            </div>
                                        </div>`;

                            $(".jpopup-content .jemergencycontentsub").append(divhtmlsub);
                        }

                        

                    }
                }
                //console.log("window location ", window.location);
                //console.log("weburl", customData.weburl);
                //console.log("window location ", window.location);

                if($(".jteTabsHNMcontainer").length){
                    if($(".jteTabsHNMcontainer .jteTabsHNMcontainerRight").length){
                        //console.log("detect HNM doctor");
                        //console.log(hospitaldata[0].ID);

                        var optionResult = false;
                        $("#jte_hnm_select option").each(function() {
                            var optionValue = $(this).val(); // Get the current option's value

                            if (optionValue == hospitaldata[0].ID) {
                                console.log('Option value ' + optionValue + ' match target id ' + hospitaldata[0].ID);
                                optionResult = true;
                            } else {

                            }
                        });

                        if(optionResult == true){
                            console.log(hospitaldata[0].ID, hospitaldata[1].ID, hospitaldata[2].ID);
                            setTimeout(function(){ 
                                // $("#jte_hnm_select").val(hospitaldata[0].ID).trigger('change');

                                $("#jte_hnm_select").val(hospitaldata[0].ID).selectmenu("refresh").trigger('selectmenuchange');
                                // $("#jte_hnm_select").trigger('selectmenuchange');
                            }, 2000);
                        } else {
                            setTimeout(function(){ 

                                $("#jte_hnm_select").val(hospitaldata[1].ID).selectmenu("refresh").trigger('selectmenuchange');

                            }, 2000);
                        }

                        
                        // setTimeout(function(){ 
                        //     if($("#jte_hnm_select").val() != hospitaldata[0].ID){
                        //         $("#jte_hnm_select").val(hospitaldata[0].ID).trigger('change');
                        //     }
                        // }, 3000);
                    }
                }

                if($(".jteTabsFADcontainer").length){
                    if($(".jteTabsFADcontainer .jteTabsFADcontainerRight").length){
                        $('.jteTabsFADcontainer input[type="submit"]').on("click", function(event) {
                            var submitcheck = true;
                            console.log("FAD Search is clicked");
                            console.log("location " + $("#jteTabsFADdoctorInput").val());
                            console.log("type " + $("#jteTabsFADspecialtyInput").data('type'));
                            if($("#jteTabsFADdoctorInput").val() == ""){
                                submitcheck = false;
                                $("#jteTabsFADdoctorInput").after('<span class="jtespanerror">Please fill the field</span>');
                            }else{
                                $(".jteTabsFADcontainer span.jtespanerror").remove();
                            }
                            if($("#jteTabsFADspecialtyInput").val() == ""){
                                submitcheck = false;
                                $("#jteTabsFADspecialtyInput").after('<span class="jtespanerror">Please fill the field</span>');
                            }else{
                                $(".jteTabsFADcontainer span.jtespanerror").remove();
                            }

                            if(submitcheck == true){
                                var customurl = "";
                                if($("#jteTabsFADspecialtyInput").data('type') == "doctor"){
                                    customurl = "&jsearch=" + encodeURIComponent($("#jteTabsFADspecialtyInput").val());
                                }else if($("#jteTabsFADspecialtyInput").data('type') == "specialty"){
                                    customurl = "&jspecialty="  + $("#jteTabsFADspecialtyInput").data("id");
                                }
                                //window.location.href = "";
                                var submiturl = customData.weburl + "/doctors/?jhospital=" + encodeURIComponent( $("#jteTabsFADdoctorInput").val()) + customurl;
                                window.location.href = submiturl;
                                //console.log();
                            }
                            //event.stopPropagation();
                            //event.preventDefault();
                            //if($("#jteTabsFADdoctorInput").val() == "")
                        });
                    }
                }

                // old MAA js
                if($(".jteTabsMAAcontainer").length){
                    if($(".jteTabsMAAcontainer .jteTabsFADcontainerRight").length){
                        $('.jteTabsMAAcontainer input[type="submit"]').on("click", function(event) {
                            var submitcheck = true;
                            console.log("MAA Search is clicked");
                            console.log("location " + $("#jteTabsMAAdoctorInput").val() + " ||| " + selectedIDMAA);
                            console.log("Specialty " + $("#jteTabsMAAspecialtyInput").val() + " ||| " + selectedID2MAA);
                            console.log("Type " + $("#jteTabsMAAspecialtyInput").val() + " ||| " + selectedType2MAA);
                            if($("#jteTabsMAAdoctorInput").val() == ""){
                                submitcheck = false;
                                $("#jteTabsMAAdoctorInput").after('<span class="jtespanerror">Please fill the field</span>');
                            }else{
                                $(".jteTabsMAAcontainer span.jtespanerror").remove();
                            }
                            if($("#jteTabsMAAspecialtyInput").val() == ""){
                                submitcheck = false;
                                $("#jteTabsMAAspecialtyInput").after('<span class="jtespanerror">Please fill the field</span>');
                            }else{
                                $(".jteTabsMAAcontainer span.jtespanerror").remove();
                            }

                            if(submitcheck == true){
                                var customurl = "";
                                if(selectedType2MAA == "doctor"){
                                    customurl = "phyID=" + selectedID2MAA + "&searchPhyAndSpec=" + encodeURIComponent($("#jteTabsMAAspecialtyInput").val());
                                }else if(selectedType2MAA == "specialty"){
                                    customurl = "searchPhyAndSpec=" + encodeURIComponent($("#jteTabsMAAspecialtyInput").val()) + '&healthcareService=' + selectedID2MAA + '&hcsType=Specialty';
                                }
                                //var submiturl = customData.weburl + "/MAA/?jhospital=" + encodeURIComponent($("#jteTabsMAAdoctorInput").val()) + "&jspecialty=" + $("#jteTabsMAAspecialtyInput").data("id");
                                //var submiturl = 'https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/%3FsearchPhyAndSpec=' + encodeURIComponent($("#jteTabsMAAspecialtyInput").val()) + '&healthcareService=' + selectedID2MAA + '&hcsType=Specialty&location=' + selectedIDMAA + '&searchLocation='+encodeURIComponent($("#jteTabsMAAdoctorInput").val());
                                var submiturl = 'https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/?' + customurl + '&location=' + selectedIDMAA + '&searchLocation='+encodeURIComponent($("#jteTabsMAAdoctorInput").val());
                                // window.location.href = submiturl;
                                window.open(submiturl, '_blank');
                                //console.log(submiturl);
                            }

                        });
                    }
                }

                if($(".jteTabsHPcontainer").length){
                    if($(".jteTabsHPcontainer .jteTabsFADcontainerRight").length){
                        // $('.jteTabsHPcontainer input[type="submit"]').on("click", function(event) {
                        //     var submitcheck = true;
                        //     console.log("HP Search is clicked");
                        //     console.log("location " + $("#jteTabsHPspecialtyInput").val());
                        //     if($("#jteTabsHPdoctorInput").val() == ""){
                        //         submitcheck = false;
                        //         $("#jteTabsHPdoctorInput").after('<span class="jtespanerror">Please fill the field</span>');
                        //     }else{
                        //         $(".jteTabsHPcontainer span.jtespanerror").remove();
                        //     }
                        //     if($("#jteTabsHPspecialtyInput").val() == ""){
                        //         submitcheck = false;
                        //         $("#jteTabsHPspecialtyInput").after('<span class="jtespanerror">Please fill the field</span>');
                        //     }else{
                        //         $(".jteTabsHPcontainer span.jtespanerror").remove();
                        //     }

                        //     console.log('url: ', $("#jteTabsMAAspecialtyInput").data("url"));
                        //     if(submitcheck == true){
                                
                        //         // var submiturl = customData.weburl + "/health-package/?jhospital=" + encodeURIComponent($("#jteTabsHPdoctorInput").val()) + "&jpackage=" + $("#jteTabsMAAspecialtyInput").data("id");
                        //         var submiturl = $("#jteTabsMAAspecialtyInput").data("url");
                        //         window.location.href = submiturl;
                                
                        //     }

                        // });
                    }
                }


                // Tabs - Make an Appointment
                if($(".jteTabsMAAcontainer")[0]){
                    console.log('jteTabsMAAcontainer EXIST!!');

                    $("#jteTabsMAAdoctorDropdown").html('<ul></ul>');
                    var loadingapi = false;
                    var showListMalaysia = [{
                        "name": "Bintulu",
                        "type": "hospital",
                        "value": [12],
                        "performingLocationId": "BINT"
                    }, {
                        "name": "Bukit Jalil",
                        "type": "hospital",
                        "value": [3273086654],
                        "performingLocationId": "BJAL"
                    }, {
                        "name": "Bukit Rimau",
                        "type": "hospital",
                        "value": [6],
                        "performingLocationId": "BRIM"
                    }, {
                        "name": "Cheras",
                        "type": "hospital",
                        "value": [18],
                        "performingLocationId": "CHRS"
                    }, {
                        "name": "Iskandar Puteri",
                        "type": "hospital",
                        "value": [22],
                        "performingLocationId": "NUSA"
                    }, {
                        "name": "Klang",
                        "type": "hospital",
                        "value": [16],
                        "performingLocationId": "KLAN"
                    }, {
                        "name": "Miri",
                        "type": "hospital",
                        "value": [10],
                        "performingLocationId": "MIRI"
                    }, {
                        "name": "Petaling Jaya",
                        "type": "hospital",
                        "value": [2],
                        "performingLocationId": "PJAY"
                    }, {
                        "name": "Puchong",
                        "type": "hospital",
                        "value": [20],
                        "performingLocationId": "PUCH"
                    }, {
                        "name": "Seremban",
                        "type": "hospital",
                        "value": [14],
                        "performingLocationId": "SRBN"
                    }, {
                        "name": "Setapak",
                        "type": "hospital",
                        "value": [4],
                        "performingLocationId": "SETA"
                    }, {
                        "name": "Shah Alam",
                        "type": "hospital",
                        "value": [2],
                        "performingLocationId": "ECSA"
                    }, {
                        "name": "Taiping",
                        "type": "hospital",
                        "value": [8],
                        "performingLocationId": "TAIP"
                    }, {
                        "name": "Tebrau",
                        "type": "hospital",
                        "value": [30],
                        "performingLocationId": "TEBR"
                    }];

                    // console.log(showListMalaysia);
                    // console.log(customhospital);

                    // $.ajax({
                    //     url: 'https://malaysiaportal.columbiaasia.com/minerva/moAppointment/fetchPractitionerAndSpeciality',
                    //     type: 'POST',
                    //     beforeSend: function(request) {
                    //         request.setRequestHeader("api-info", "V1|appVerson|deviceBrand|deviceModel|deviceScreenResolution|deviceOs|deviceOsVersion|deviceNetworkProvider|deviceNetworkType");
                    //     },
                    //     data: {
                    //         "constraints": {
                    //             "locationIDs" : [2],
                    //             "_skip" : 0,
                    //             "_count" : 100
                    //         }
                    //     },
                    //     success: function(response) {                   
                    //         console.log('response from AJAX fetchPractitionerAndSpeciality: ', response);
                            
                    //     },
                    //     error: function(XMLHttpRequest, textStatus, errorThrown) { 
                    //         console.log("Status: " + textStatus); 
                    //         console.log("Error: " + errorThrown); 
                    //         console.log(XMLHttpRequest);
                    //     } 

                    // });


                    // Hospital
                    var $inputMAA = $('#jteTabsMAAdoctorInput');
                    var $dropdownMAA = $('#jteTabsMAAdoctorDropdown');
                    var initialItemsMAA = $dropdownMAA.html(); // Store the initial items

                    var $input2MAA = $('#jteTabsMAAspecialtyInput');
                    var $dropdown2MAA = $('#jteTabsMAAspecialtyDropdown');
                    var initialItems2MAA = $dropdown2MAA.html(); // Store the initial items

                    var selectedTitleMAA = '';
                    var selectedIDMAA = '';
                    var divhtml = '';
                    var templist = $("#jteTabsMAAdoctorDropdown").children('ul');

                    for (let i = 0; i < showListMalaysia.length; i++) {
                        //rightContainer01MAA.append('<li class="doctorList"><a href="#"><span class="title" data-value="'+ response.doctor[i].post_id +'">'+ response.doctor[i].post_name +'</span><span class="subtitle">Doctor</span></a></li>');
                        templist.append('<li><a href="#"><span class="title" data-value="'+ showListMalaysia[i].value[0] +'">'+ showListMalaysia[i].name +'</span></a></li>');
                        //console.log(showListMalaysia[i].name + " ||| " + showListMalaysia[i].value[0]);
                    }

                    initialItemsMAA = $dropdownMAA.html();

                      // Slide down the dropdown when the input field is clicked
                    $inputMAA.on('click', function() {
                        $dropdownMAA.show();
                        // filterItemsMAA();
                    });

                      // Event handler for input changes
                    $inputMAA.on('input', function() {
                        filterItemsMAA();
                    });

                    // Handle item selection by clicking
                    $dropdownMAA.on('click', 'a', function(e) {
                        e.preventDefault();

                        var selectedTextMAA = $(this).find('.title').text().trim();
                        console.log('selectedTextMAA: ', $(this).html());
                        $inputMAA.val(selectedTextMAA);
                        selectedTitleMAA = $(this).children().text();
                        selectedIDMAA = $(this).children().data( "value" );
                        $inputMAA.data("id", selectedIDMAA);
                        $dropdownMAA.hide();
                        $("#jteTabsMAAspecialtyInput").val('');

                        console.log('selectedTitleMAA: ', selectedTitleMAA);
                        console.log('selectedIDMAA: ', selectedIDMAA);

                        var jtemparr = [selectedIDMAA];
                        //console.log("jtemparr", jtemparr);
                        // AJAX Call
                        var rightContainerMAA = $(this).parent().parent().parent().parent().siblings('.jteTabsFADspecialty');
                        var rightContainer01MAA = $(this).parent().parent().parent().parent().siblings('.jteTabsFADspecialty').children('.jteTabsFADspecialtyDropdownContainer').children('ul');
                        loadingapi = true;
                        var doctor_arr;
                        $.ajax({
                            url: '/malaysia/wp-admin/admin-ajax.php', // Use the URL to your WordPress admin-ajax.php file
                            type: 'POST',
                            data: {
                                action: 'custom_ajax_alldoctor_callback',
                                hospital: selectedTitleMAA
                            },
                            success: function(response) {                    
                                console.log('response from AJAX MAA All doctor: ', response);
                                doctor_arr = response.data;
                                // console.log('rightContainer01: ', rightContainer01.html());
                             // rightContainer01MAA.children().remove();
                             // for (let i = 0; i < response.doctor.length; i++) {
                             //     rightContainer01MAA.append('<li class="doctorList"><a href="#"><span class="title" data-value="'+ response.doctor[i].post_id +'">'+ response.doctor[i].post_name +'</span><span class="subtitle">Doctor</span></a></li>');
                             // }  

                             // initialItems2MAA = $dropdown2MAA.html(); // Store the initial items

                                $.ajax({
                                    url: 'https://malaysiaportal.columbiaasia.com/minerva/moAppointment/fetchPractitionerAndSpeciality', // Use the URL to your WordPress admin-ajax.php file
                                    type: 'POST',
                                    beforeSend: function(request) {
                                        request.setRequestHeader("api-info", "V1|appVerson|deviceBrand|deviceModel|deviceScreenResolution|deviceOs|deviceOsVersion|deviceNetworkProvider|deviceNetworkType");
                                    },
                                    data: JSON.stringify({
                                        "constraints": {
                                            "locationIDs" : jtemparr,
                                            "_skip" : 0,
                                            "_count" : 100
                                        }
                                    }),
                                    contentType: 'application/json',
                                    success: function(response) {                   
                                        console.log('response from AJAX MAA: ', response);
                                        // console.log('rightContainer01: ', rightContainer01.html());
                                        rightContainer01MAA.children().remove();



                                        for (let i = 0; i < doctor_arr.length; i++) {
                                             rightContainer01MAA.append('<li class="doctorList"><a href="#"><span class="title" data-value="'+ doctor_arr[i].doctorID +'">'+ doctor_arr[i].post_title +'</span><span class="subtitle">Doctor</span></a></li>');
                                         }
                                        
                                        for (let i = 0; i < response.hcsList.length; i++) {
                                            var nameToUC = response.hcsList[i].name.toLowerCase();
                                            console.log('nameToUC: ', nameToUC, nameToUC.toUpperCase());

                                            rightContainer01MAA.append('<li><a href="#"><span class="title" data-value="'+ response.hcsList[i].id[0] +'">'+ response.hcsList[i].name.toLowerCase() +'</span><span class="subtitle">Specialty</span></a></li>');
                                        }   
                                        loadingapi = false;
                                        initialItems2MAA = $dropdown2MAA.html(); // Store the initial items
                                    },
                                    error: function(XMLHttpRequest, textStatus, errorThrown) { 
                                        console.log("Status: " + textStatus); 
                                        console.log("Error: " + errorThrown); 
                                        console.log(XMLHttpRequest);
                                    } 

                                });


                            },
                            error: function(XMLHttpRequest, textStatus, errorThrown) { 
                                console.log("Status: " + textStatus); 
                                console.log("Error: " + errorThrown); 
                                console.log(XMLHttpRequest);
                            } 

                        });

                        

                    });

                    // Hide dropdown when clicking outside of the dropdown
                    $(document).on('click', function(e) {
                        if (!$inputMAA.is(e.target) && !$dropdownMAA.is(e.target) && $dropdownMAA.has(e.target).length === 0) {
                            $dropdownMAA.hide();
                        }
                    });

                    function filterItemsMAA() {
                        var keywordMAA = $inputMAA.val().trim().toLowerCase();
                        if (keywordMAA === '') {
                            // If the input is empty, restore the initial items
                            $dropdownMAA.html(initialItemsMAA);
                            return;
                        }
                        $dropdownMAA.find('li').each(function() {
                            var $itemMAA = $(this);

                            // var title = $item.find('a').text().split(' ')[0].toLowerCase(); // Extract only the title and convert to lowercase
                            var titleMAA = $itemMAA.find('a').children('.title').text().toLowerCase(); // Extract only the title and convert to lowercase
                            // var title = $item.find('.title').text().toLowerCase();

                            var $subtitleMAA = $itemMAA.find('.subtitle');
                            var foundMAA = titleMAA.includes(keywordMAA);

                            if (foundMAA) {
                                $dropdownMAA.show();
                                // Highlight the matching text in the title with the "highlight" class

                                // var originalText = $item.find('a').text();
                                var originalTextMAA = $itemMAA.find('a').children('.title').text();
                                console.log('originalTextMAA: ', originalTextMAA);

                                var highlightedTextMAA = originalTextMAA.replace(new RegExp(keywordMAA, 'gi'), function(match) {
                                  return '<b>' + match + '</b>';
                                });

                                // var regExp = new RegExp(keyword, 'gi');

                                // $item.find('a').html(highlightedText);
                                $itemMAA.find('.title').html(highlightedTextMAA);

                                $subtitleMAA.show();
                                $itemMAA.show();
                            } else {
                                console.log('NOT FOUND!!');
                                $subtitleMAA.hide();
                                $itemMAA.hide();
                                if(!$dropdownMAA.children().children('li').is(':visible')){
                                    console.log('ALL HIDDEN');
                                    $dropdownMAA.hide();
                                } else {
                                    console.log('SOME HIDDEN');
                                    $dropdownMAA.show();
                                }
                                // $dropdown.hide();
                          }
                        });
                     }


                     // Specialty
                    

                    var selectedTitle2MAA = '';
                    var selectedID2MAA = '';
                    var selectedType2MAA = '';

                    console.log('$input val MAA:', $inputMAA.val());

                      // Slide down the dropdown when the input field is clicked
                    $input2MAA.on('click', function() {
                        // if(loadingapi == false){
                            if($inputMAA.val() != ''){
                                $(this).siblings('.jteTabsFADspecialtyErr').hide();
                                $dropdown2MAA.show();
                                // filterItems2MAA();
                            } else {
                                // console.log('selectedTitle BEFORE:', selectedTitle);
                                // console.log('selectedID BEFORE:', selectedID);
                                $(this).val('');
                                $(this).siblings('.jteTabsFADspecialtyErr').show();
                                selectedTitleMAA = '';
                                selectedIDMAA = '';
                                // console.log('selectedTitle AFTER:', selectedTitle);
                                // console.log('selectedID AFTER:', selectedID);
                            }
                        // }
                    });

                      // Event handler for input changes
                    $input2MAA.on('input', function() {
                        filterItems2MAA();
                    });

                    // Handle item selection by clicking
                    $dropdown2MAA.on('click', 'a', function(e) {
                        e.preventDefault();
                        var selectedText2MAA = $(this).find('.title').text().trim();
                        console.log('selectedText2MAA: ', $(this).html());
                        $input2MAA.val(selectedText2MAA);
                        selectedTitle2MAA = $(this).children().text();
                        selectedID2MAA = $(this).children().data( "value" );
                        if($( this ).parent().hasClass( "doctorList" )){
                            selectedType2MAA = "doctor";
                        }else{
                            selectedType2MAA = "specialty";
                        }
                        $dropdown2MAA.hide();

                        console.log('selectedTitle2MAA: ', selectedTitle2MAA);
                        console.log('selectedID2MAA: ', selectedID2MAA);
                    });

                    // Hide dropdown when clicking outside of the dropdown
                    $(document).on('click', function(e) {
                        if (!$input2MAA.is(e.target) && !$dropdown2MAA.is(e.target) && $dropdown2MAA.has(e.target).length === 0) {
                            $dropdown2MAA.hide();
                        }
                    });

                    function filterItems2MAA() {
                        var keyword2MAA = $input2MAA.val().trim().toLowerCase();
                        if (keyword2MAA === '') {
                            // If the input is empty, restore the initial items
                            $dropdown2MAA.html(initialItems2MAA);
                            return;
                        }

                        console.log('keyword2MAA: ', keyword2MAA);
                        $dropdown2MAA.find('li').each(function() {
                            var $item2MAA = $(this);

                            // var title = $item.find('a').text().split(' ')[0].toLowerCase(); // Extract only the title and convert to lowercase
                            var title2MAA = $item2MAA.find('a').children('.title').text().toLowerCase(); // Extract only the title and convert to lowercase
                            // var title = $item.find('.title').text().toLowerCase();

                            var $subtitle2MAA = $item2MAA.find('.subtitle');
                            var found2MAA = title2MAA.includes(keyword2MAA);

                            if (found2MAA) {
                                $dropdown2MAA.show();
                                // Highlight the matching text in the title with the "highlight" class

                                // var originalText = $item.find('a').text();
                                var originalText2MAA = $item2MAA.find('a').children('.title').text();
                                console.log('originalText2MAA: ', originalText2MAA);

                                var highlightedText2MAA = originalText2MAA.replace(new RegExp(keyword2MAA, 'gi'), function(match2) {
                                  return '<b>' + match2 + '</b>';
                                });

                                // var regExp = new RegExp(keyword, 'gi');

                                // $item.find('a').html(highlightedText);
                                $item2MAA.find('.title').html(highlightedText2MAA);

                                $subtitle2MAA.show();
                                $item2MAA.show();
                            } else {
                                $subtitle2MAA.hide();
                                $item2MAA.hide();
                                if(!$dropdown2MAA.children().children('li').is(':visible')){
                                    console.log('ALL HIDDEN');
                                    $dropdown2MAA.hide();
                                } else {
                                    console.log('SOME HIDDEN');
                                    $dropdown2MAA.show();
                                }
                          }
                        });
                     }


                }

            //end geotargetly depandancy function
        }

        window.jmanualgeotargetly = jmanualgeotargetly;
    });

    

})(jQuery);