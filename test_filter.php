<?php
/**
 * Quick test for hospital filtering
 * Access via: /test_filter.php
 */

// Include WordPress
require_once( get_template_directory() . '/lib/init.php' );

echo "<h1>Hospital Filter Test</h1>";

// Test 1: Check all doctors and their hospital fields
echo "<h2>All Doctors and Their Hospital Fields</h2>";

$all_doctors_args = array(
    'post_type' => 'doctor',
    'posts_per_page' => 10,
    'post_status' => 'publish',
    'meta_query' => array(
        array(
            'key' => 'show_in_listing',
            'value' => '0',
            'compare' => '!=',
            'type' => 'BINARY'
        )
    )
);

$all_doctors = new WP_Query($all_doctors_args);

echo "<p>Found " . $all_doctors->found_posts . " total doctors</p>";

if ($all_doctors->have_posts()) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Doctor Name</th><th>Hospital Field</th><th>Appointment List</th></tr>";
    
    while ($all_doctors->have_posts()) {
        $all_doctors->the_post();
        $hospital_field = get_field('hospital');
        $appointment_list = get_field('appointment_list');
        
        echo "<tr>";
        echo "<td>" . get_the_title() . "</td>";
        echo "<td>";
        
        if (is_array($hospital_field)) {
            foreach ($hospital_field as $hospital_post) {
                echo $hospital_post->post_title . " (ID: " . $hospital_post->post_excerpt . ")<br>";
            }
        } else {
            echo "Not an array or empty";
        }
        
        echo "</td>";
        echo "<td>" . esc_html($appointment_list) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    wp_reset_postdata();
}

// Test 2: Check Batu Kawan hospital
echo "<h2>Batu Kawan Hospital Info</h2>";

$hospital_args = array(
    'post_type' => 'hospital',
    'post_status' => 'publish',
    'title' => 'Batu Kawan',
    'numberposts' => 1
);

$hospitals = get_posts($hospital_args);

if ($hospitals) {
    $hospital = $hospitals[0];
    echo "<p><strong>Hospital Found:</strong></p>";
    echo "<ul>";
    echo "<li>Title: " . $hospital->post_title . "</li>";
    echo "<li>ID: " . $hospital->ID . "</li>";
    echo "<li>Excerpt (Hospital ID): " . $hospital->post_excerpt . "</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>Batu Kawan hospital not found!</p>";
}

// Test 3: Test filtering by hospital field
echo "<h2>Test: Filter by Hospital Field</h2>";

$filter_args = array(
    'post_type' => 'doctor',
    'posts_per_page' => 10,
    'post_status' => 'publish',
    'meta_query' => array(
        'relation' => 'AND',
        array(
            'key' => 'show_in_listing',
            'value' => '0',
            'compare' => '!=',
            'type' => 'BINARY'
        ),
        array(
            'key' => 'hospital',
            'value' => 'Batu Kawan',
            'compare' => 'LIKE'
        )
    )
);

$filtered_doctors = new WP_Query($filter_args);
echo "<p>Doctors with hospital field containing 'Batu Kawan': " . $filtered_doctors->found_posts . "</p>";

if ($filtered_doctors->have_posts()) {
    echo "<ul>";
    while ($filtered_doctors->have_posts()) {
        $filtered_doctors->the_post();
        echo "<li>" . get_the_title() . "</li>";
    }
    echo "</ul>";
    wp_reset_postdata();
}

// Test 4: Test filtering by appointment_list
if ($hospitals) {
    $hospital_id = $hospitals[0]->post_excerpt;
    
    echo "<h2>Test: Filter by Appointment List (Hospital ID: " . $hospital_id . ")</h2>";
    
    $appointment_filter_args = array(
        'post_type' => 'doctor',
        'posts_per_page' => 10,
        'post_status' => 'publish',
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => 'show_in_listing',
                'value' => '0',
                'compare' => '!=',
                'type' => 'BINARY'
            ),
            array(
                'key' => 'appointment_list',
                'value' => $hospital_id . '-',
                'compare' => 'LIKE'
            )
        )
    );
    
    $appointment_filtered_doctors = new WP_Query($appointment_filter_args);
    echo "<p>Doctors with appointment_list containing '" . $hospital_id . "-': " . $appointment_filtered_doctors->found_posts . "</p>";
    
    if ($appointment_filtered_doctors->have_posts()) {
        echo "<ul>";
        while ($appointment_filtered_doctors->have_posts()) {
            $appointment_filtered_doctors->the_post();
            $appointment_list = get_field('appointment_list');
            echo "<li>" . get_the_title() . " - " . esc_html($appointment_list) . "</li>";
        }
        echo "</ul>";
        wp_reset_postdata();
    }
}

echo "<p><a href='?'>Refresh</a></p>";
?>
