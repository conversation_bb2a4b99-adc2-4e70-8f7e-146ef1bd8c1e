<?php
/**
 * Debug script for <PERSON><PERSON> appointment button issue
 * This script helps identify why appointment buttons are not showing for <PERSON>u <PERSON> doctors
 */

// Include WordPress
require_once( get_template_directory() . '/lib/init.php' );

// Function to debug doctor appointment data
function debug_doctor_appointments($doctor_id = null) {
    echo "<h2>Debugging Batu Kawan Doctor Appointments</h2>";
    
    // Query for doctors
    $args = array(
        'post_type' => 'doctor',
        'posts_per_page' => 10, // Limit for debugging
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => 'show_in_listing',
                'value' => '0',
                'compare' => '!=',
                'type' => 'BINARY'
            )
        )
    );
    
    if ($doctor_id) {
        $args['p'] = $doctor_id;
    }
    
    $doctors = get_posts($args);
    
    echo "<p>Found " . count($doctors) . " doctors</p>";
    
    foreach ($doctors as $doctor) {
        $doctor_name = $doctor->post_title;
        $appointment_list = get_field('appointment_list', $doctor->ID);
        $hospital_field = get_field('hospital', $doctor->ID);
        
        echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
        echo "<h3>Doctor: " . esc_html($doctor_name) . " (ID: " . $doctor->ID . ")</h3>";
        echo "<p><strong>Appointment List:</strong> " . esc_html($appointment_list) . "</p>";
        
        // Parse appointment list
        if (!empty($appointment_list)) {
            $commaSeparated = explode(", ", $appointment_list);
            echo "<p><strong>Parsed Appointments:</strong></p>";
            echo "<ul>";
            
            foreach ($commaSeparated as $element) {
                if (strpos($element, '-') !== false) {
                    list($hospital_id, $status) = explode("-", $element);
                    echo "<li>Hospital ID: " . esc_html($hospital_id) . " | Status: " . esc_html($status) . "</li>";
                    
                    // Find hospital name
                    if (is_array($hospital_field)) {
                        foreach ($hospital_field as $hospital_post) {
                            if ((int)trim($hospital_id) == (int)trim($hospital_post->post_excerpt)) {
                                echo "<li style='margin-left: 20px;'>→ Hospital Name: " . esc_html($hospital_post->post_title) . "</li>";
                                
                                // Check if this is Batu Kawan and status is 2
                                if ($hospital_post->post_title == "Batu Kawan" && $status == 2) {
                                    echo "<li style='margin-left: 40px; color: green;'>✓ Should show Batu Kawan appointment button!</li>";
                                } elseif ($hospital_post->post_title == "Batu Kawan" && $status != 2) {
                                    echo "<li style='margin-left: 40px; color: red;'>✗ Batu Kawan doctor but status is not 2 (status: " . $status . ")</li>";
                                }
                            }
                        }
                    }
                }
            }
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>No appointment list found!</p>";
        }
        
        // Show hospital field data
        echo "<p><strong>Hospital Field Data:</strong></p>";
        if (is_array($hospital_field)) {
            echo "<ul>";
            foreach ($hospital_field as $hospital_post) {
                echo "<li>Hospital: " . esc_html($hospital_post->post_title) . " (ID: " . $hospital_post->ID . ", Excerpt: " . esc_html($hospital_post->post_excerpt) . ")</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>Hospital field is not an array or is empty</p>";
        }
        
        echo "</div>";
    }
}

// Check if this is being called directly
if (isset($_GET['debug_batu_kawan'])) {
    debug_doctor_appointments();
    exit;
}

// Also check for specific doctor ID
if (isset($_GET['debug_doctor_id'])) {
    debug_doctor_appointments((int)$_GET['debug_doctor_id']);
    exit;
}

// Function to check Batu Kawan hospital data
function debug_batu_kawan_hospital() {
    echo "<h2>Debugging Batu Kawan Hospital Data</h2>";
    
    $hospital_args = array(
        'post_type' => 'hospital',
        'post_status' => 'publish',
        'title' => 'Batu Kawan',
        'numberposts' => 1
    );
    
    $hospitals = get_posts($hospital_args);
    
    if ($hospitals) {
        $hospital = $hospitals[0];
        echo "<p><strong>Batu Kawan Hospital Found:</strong></p>";
        echo "<ul>";
        echo "<li>Title: " . esc_html($hospital->post_title) . "</li>";
        echo "<li>ID: " . $hospital->ID . "</li>";
        echo "<li>Excerpt (Hospital ID): " . esc_html($hospital->post_excerpt) . "</li>";
        echo "</ul>";
        
        // Get custom fields
        $custom_fields = get_fields($hospital->ID);
        if ($custom_fields) {
            echo "<p><strong>Custom Fields:</strong></p>";
            echo "<pre>" . print_r($custom_fields, true) . "</pre>";
        }
    } else {
        echo "<p style='color: red;'>Batu Kawan hospital not found!</p>";
    }
}

if (isset($_GET['debug_hospital'])) {
    debug_batu_kawan_hospital();
    exit;
}

// Function to test hospital filtering
function test_hospital_filtering() {
    echo "<h2>Testing Hospital Filtering Logic</h2>";

    // Test the same logic as archive-doctor.php
    $hospital = "Batu Kawan";

    echo "<p><strong>Testing filter for hospital:</strong> " . esc_html($hospital) . "</p>";

    // Step 1: Find hospital post
    $jtempargs = array(
        'post_type' => 'hospital',
        'post_status' => 'publish',
        'numberposts' => 1,
        'title' => $hospital,
        'fields' => 'ids'
    );

    $jtemppost = get_posts($jtempargs);

    if ($jtemppost) {
        $hospital_id = $jtemppost[0];
        $hospital_excerpt = get_the_excerpt($hospital_id);
        echo "<p>✓ Found hospital post ID: " . $hospital_id . "</p>";
        echo "<p>✓ Hospital excerpt (ID): " . esc_html($hospital_excerpt) . "</p>";

        // Step 2: Test meta queries
        echo "<h3>Testing Meta Queries</h3>";

        // Test 1: Filter by hospital relationship
        $meta_query1 = array(
            array(
                'key' => 'hospital',
                'value' => $hospital,
                'compare' => 'LIKE'
            )
        );

        $args1 = array(
            'post_type' => 'doctor',
            'posts_per_page' => 5,
            'post_status' => 'publish',
            'meta_query' => $meta_query1
        );

        $query1 = new WP_Query($args1);
        echo "<p><strong>Query 1 (hospital field LIKE 'Batu Kawan'):</strong> Found " . $query1->found_posts . " doctors</p>";

        // Test 2: Filter by appointment_list
        $meta_query2 = array(
            array(
                'key' => 'appointment_list',
                'value' => $hospital_excerpt . '-',
                'compare' => 'LIKE'
            )
        );

        $args2 = array(
            'post_type' => 'doctor',
            'posts_per_page' => 5,
            'post_status' => 'publish',
            'meta_query' => $meta_query2
        );

        $query2 = new WP_Query($args2);
        echo "<p><strong>Query 2 (appointment_list LIKE '" . $hospital_excerpt . "-'):</strong> Found " . $query2->found_posts . " doctors</p>";

        // Test 3: Combined query (AND)
        $meta_query3 = array(
            'relation' => 'AND',
            array(
                'key' => 'hospital',
                'value' => $hospital,
                'compare' => 'LIKE'
            ),
            array(
                'key' => 'appointment_list',
                'value' => $hospital_excerpt . '-',
                'compare' => 'LIKE'
            )
        );

        $args3 = array(
            'post_type' => 'doctor',
            'posts_per_page' => 5,
            'post_status' => 'publish',
            'meta_query' => $meta_query3
        );

        $query3 = new WP_Query($args3);
        echo "<p><strong>Query 3 (Combined AND):</strong> Found " . $query3->found_posts . " doctors</p>";

        // Show some sample doctors from each query
        if ($query1->have_posts()) {
            echo "<h4>Sample doctors from Query 1:</h4><ul>";
            while ($query1->have_posts()) {
                $query1->the_post();
                $appointment_list = get_field('appointment_list');
                echo "<li>" . get_the_title() . " - appointment_list: " . esc_html($appointment_list) . "</li>";
            }
            echo "</ul>";
            wp_reset_postdata();
        }

    } else {
        echo "<p style='color: red;'>✗ Hospital post not found for: " . esc_html($hospital) . "</p>";
    }
}

if (isset($_GET['test_filtering'])) {
    test_hospital_filtering();
    exit;
}

// Function to test single doctor appointment logic
function test_single_doctor_logic($doctor_id) {
    echo "<h2>Testing Single Doctor Appointment Logic</h2>";

    $doctor = get_post($doctor_id);
    if (!$doctor) {
        echo "<p style='color: red;'>Doctor not found with ID: " . $doctor_id . "</p>";
        return;
    }

    echo "<p><strong>Testing doctor:</strong> " . esc_html($doctor->post_title) . " (ID: " . $doctor_id . ")</p>";

    // Get the same fields as single-doctor.php
    $jdoctorid = get_field('doctorid', $doctor_id);
    $jdoctorname = $doctor->post_title;
    $tempappointment = get_field('appointment_list', $doctor_id);
    $location = get_field('hospital', $doctor_id);

    echo "<p><strong>Doctor ID:</strong> " . esc_html($jdoctorid) . "</p>";
    echo "<p><strong>Doctor Name:</strong> " . esc_html($jdoctorname) . "</p>";
    echo "<p><strong>Appointment List:</strong> " . esc_html($tempappointment) . "</p>";

    // Process appointment list like single-doctor.php
    $appointment_arr = array();
    $commaSeparated = explode(", ", $tempappointment);

    if (!empty($tempappointment)) {
        echo "<h3>Processing Appointment List</h3>";

        foreach ($commaSeparated as $element) {
            if (strpos($element, '-') !== false) {
                list($jhospital_id, $status) = explode("-", $element);
                $temphospitalname = "";

                echo "<p>Processing: Hospital ID " . esc_html($jhospital_id) . " with status " . esc_html($status) . "</p>";

                // Find hospital name
                if (is_array($location)) {
                    foreach ($location as $post_object) {
                        if ((int)trim($jhospital_id) == (int)trim($post_object->post_excerpt)) {
                            $temphospitalname = $post_object->post_title;
                            echo "<p style='margin-left: 20px;'>→ Matched hospital: " . esc_html($temphospitalname) . "</p>";
                            break;
                        }
                    }
                }

                // Check appointment creation logic
                if ($status == 2 && $temphospitalname != "") {
                    if ($temphospitalname == "Batu Kawan") {
                        $appointment_arr[] = "https://www.columbiaasia.com/malaysia/hospitals/batu-kawan#batu-kawan-maa";
                        echo "<p style='margin-left: 20px; color: green;'>✓ Added Batu Kawan appointment URL</p>";
                    } else {
                        $appointment_arr[] = "https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/%3FphyID=".$jdoctorid."&searchPhyAndSpec=".str_replace('/', '%252F', $jdoctorname)."&location=".$jhospital_id."&searchLocation=".$temphospitalname."";
                        echo "<p style='margin-left: 20px; color: blue;'>✓ Added standard appointment URL for " . esc_html($temphospitalname) . "</p>";
                    }
                } else {
                    echo "<p style='margin-left: 20px; color: red;'>✗ No appointment URL added - Status: " . esc_html($status) . ", Hospital: " . esc_html($temphospitalname) . "</p>";
                }
            }
        }
    } else {
        echo "<p style='color: red;'>Appointment list is empty!</p>";
    }

    echo "<h3>Final Results</h3>";
    echo "<p><strong>Appointment URLs found:</strong> " . count($appointment_arr) . "</p>";

    if (!empty($appointment_arr)) {
        echo "<ul>";
        foreach ($appointment_arr as $url) {
            echo "<li>" . esc_html($url) . "</li>";
        }
        echo "</ul>";

        // Test button display logic
        echo "<h4>Button Display Test</h4>";
        if (strpos(implode("|||", $appointment_arr), "|||") !== false) {
            echo "<p>Would show popup button (multiple appointments)</p>";
        } else {
            $appointment_str = implode("|||", $appointment_arr);
            if (strpos($appointment_str, 'batu-kawan') !== false) {
                echo "<p style='color: green;'>Would show Batu Kawan appointment button</p>";
                echo "<p>Button HTML: <code>&lt;a href=\"" . esc_html($appointment_str) . "\" class=\"raven-button appointment-btn\"&gt;Make An Appointment&lt;/a&gt;</code></p>";
            } else {
                echo "<p>Would show standard appointment button</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>No appointment button would be displayed</p>";
    }
}

if (isset($_GET['test_single_doctor']) && isset($_GET['doctor_id'])) {
    test_single_doctor_logic((int)$_GET['doctor_id']);
    exit;
}
?>
