<?php
/**
 * Test single doctor appointment logic
 * Access via: /test_single_doctor.php?doctor_id=123
 */

// Include WordPress
require_once( get_template_directory() . '/lib/init.php' );

if (!isset($_GET['doctor_id'])) {
    echo "<h1>Single Doctor Test</h1>";
    echo "<p>Please provide a doctor ID: <code>?doctor_id=123</code></p>";
    
    // Show some available doctors
    echo "<h2>Available Doctors</h2>";
    $doctors = get_posts(array(
        'post_type' => 'doctor',
        'posts_per_page' => 10,
        'post_status' => 'publish'
    ));
    
    echo "<ul>";
    foreach ($doctors as $doctor) {
        echo "<li><a href='?doctor_id=" . $doctor->ID . "'>" . esc_html($doctor->post_title) . " (ID: " . $doctor->ID . ")</a></li>";
    }
    echo "</ul>";
    exit;
}

$doctor_id = (int)$_GET['doctor_id'];
$doctor = get_post($doctor_id);

if (!$doctor || $doctor->post_type !== 'doctor') {
    echo "<h1>Error</h1>";
    echo "<p>Doctor not found or invalid ID: " . $doctor_id . "</p>";
    exit;
}

echo "<h1>Single Doctor Appointment Test</h1>";
echo "<h2>Doctor: " . esc_html($doctor->post_title) . " (ID: " . $doctor_id . ")</h2>";

// Simulate the exact same logic as single-doctor.php
global $post;
$post = $doctor;
setup_postdata($post);

echo "<h3>Step 1: Get Basic Data</h3>";
$doctor_name = get_the_title();
$current_post_id = get_the_ID();
echo "<p>WordPress Title: " . esc_html($doctor_name) . "</p>";
echo "<p>WordPress ID: " . $current_post_id . "</p>";

echo "<h3>Step 2: Get Custom Fields</h3>";
$jdoctorid = get_field('doctorid', $current_post_id);
$tempappointment = get_field("appointment_list", $current_post_id);
$location = get_field("hospital", $current_post_id);

echo "<p>Doctor ID Field: " . esc_html($jdoctorid) . "</p>";
echo "<p>Appointment List: " . esc_html($tempappointment) . "</p>";
echo "<p>Hospital Field: ";
if (is_array($location)) {
    echo "Array with " . count($location) . " hospitals<br>";
    foreach ($location as $hospital) {
        echo "- " . esc_html($hospital->post_title) . " (ID: " . esc_html($hospital->post_excerpt) . ")<br>";
    }
} else {
    echo "Not an array or empty";
}
echo "</p>";

echo "<h3>Step 3: Process Appointment List</h3>";
$appointment_arr = array();
$jdoctorname = $doctor_name;

if (!empty($tempappointment)) {
    $commaSeparated = explode(", ", $tempappointment);
    echo "<p>Split into " . count($commaSeparated) . " parts: " . implode(" | ", $commaSeparated) . "</p>";
    
    foreach ($commaSeparated as $element) {
        if (strpos($element, '-') !== false) {
            list($jhospital_id, $status) = explode("-", $element);
            $temphospitalname = "";
            
            echo "<p>Processing: Hospital ID " . esc_html($jhospital_id) . " with status " . esc_html($status) . "</p>";
            
            // Find hospital name
            if (is_array($location)) {
                foreach ($location as $post_object) {
                    if ((int)trim($jhospital_id) == (int)trim($post_object->post_excerpt)) {
                        $temphospitalname = $post_object->post_title;
                        echo "<p style='margin-left: 20px;'>→ Matched hospital: " . esc_html($temphospitalname) . "</p>";
                        break;
                    }
                }
            }
            
            // Check appointment creation logic
            echo "<p style='margin-left: 20px;'>Status check: " . ($status == 2 ? "✓" : "✗") . " Status is 2</p>";
            echo "<p style='margin-left: 20px;'>Hospital check: " . ($temphospitalname != "" ? "✓" : "✗") . " Hospital name found</p>";
            
            if ($status == 2 && $temphospitalname != "") {
                if ($temphospitalname == "Batu Kawan") {
                    $appointment_arr[] = "https://www.columbiaasia.com/malaysia/hospitals/batu-kawan#batu-kawan-maa";
                    echo "<p style='margin-left: 20px; color: green;'>✓ Added Batu Kawan appointment URL</p>";
                } else {
                    $appointment_arr[] = "https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/%3FphyID=".$jdoctorid."&searchPhyAndSpec=".str_replace('/', '%252F', $jdoctorname)."&location=".$jhospital_id."&searchLocation=".$temphospitalname."";
                    echo "<p style='margin-left: 20px; color: blue;'>✓ Added standard appointment URL for " . esc_html($temphospitalname) . "</p>";
                }
            } else {
                echo "<p style='margin-left: 20px; color: red;'>✗ No appointment URL added</p>";
            }
        }
    }
} else {
    echo "<p style='color: red;'>Appointment list is empty or null!</p>";
}

echo "<h3>Step 4: Final Results</h3>";
echo "<p><strong>Total appointment URLs:</strong> " . count($appointment_arr) . "</p>";

if (!empty($appointment_arr)) {
    echo "<h4>Appointment URLs:</h4>";
    echo "<ul>";
    foreach ($appointment_arr as $url) {
        echo "<li>" . esc_html($url) . "</li>";
    }
    echo "</ul>";
    
    echo "<h4>Button Display Logic:</h4>";
    if (strpos(implode("|||", $appointment_arr), "|||") !== false) {
        echo "<p>Would show: <strong>Popup button</strong> (multiple appointments)</p>";
    } else {
        $appointment_str = implode("|||", $appointment_arr);
        if (strpos($appointment_str, 'batu-kawan') !== false) {
            echo "<p style='color: green;'>Would show: <strong>Batu Kawan appointment button</strong></p>";
            echo "<div style='border: 1px solid green; padding: 10px; margin: 10px 0;'>";
            echo "<strong>Button HTML:</strong><br>";
            echo "<code>&lt;a href=\"" . esc_html($appointment_str) . "\" class=\"raven-button appointment-btn\" data-doctor=\"" . esc_attr($jdoctorname) . "\" title=\"Doctor: " . esc_attr($jdoctorname) . "\"&gt;Make An Appointment&lt;/a&gt;</code>";
            echo "</div>";
        } else {
            echo "<p>Would show: <strong>Standard appointment button</strong></p>";
        }
    }
} else {
    echo "<p style='color: red; font-weight: bold;'>NO APPOINTMENT BUTTON WOULD BE DISPLAYED</p>";
    echo "<p>This is likely why the button is not appearing on the single doctor page.</p>";
}

wp_reset_postdata();

echo "<p><a href='?'>← Back to doctor list</a></p>";
?>
