<?php
// Include Jupiter X.
require_once( get_template_directory() . '/lib/init.php' );
// var_dump("dddddddddd");
// acf_form_head();

get_header();


// jupiterx_open_markup_e( 'jupiterx_head', 'head' );

// 	/**
// 	 * Fires in the head.
// 	 *
// 	 * This hook fires in the head HTML section, not in wp_header().
// 	 *
// 	 * @since 1.0.0
// 	 */
// 	do_action( 'jupiterx_head' );

// 	wp_head();

// jupiterx_close_markup_e( 'jupiterx_head', 'head' );

// jupiterx_open_markup_e(
// 	'jupiterx_body',
// 	'body',
// 	[
// 		'class'     => esc_attr( implode( ' ', get_body_class( 'no-js' ) ) ),
// 		'itemscope' => 'itemscope',
// 		'itemtype'  => 'http://schema.org/WebPage',
// 	]
// );



// echo '<div class="jupiterx-site" style="">';

// // jupiterx_header_partial_template();

// // jupiterx_main_header_partial_template();

// jupiterx_content_template();

// // jupiterx_main_header_post_title();
// // jupiterx_post_title();
// // jupiterx_post_archive_title();
// // jupiterx_subtitle();
// // jupiterx_post_meta();
// // jupiterx_post_image();

// jupiterx_main_footer_partial_template();

// jupiterx_loop_template( $id = false );

// jupiterx_footer_partial_template();

// jupiterx_scroll_top_button();

// echo '</div>';

// jupiterx_replace_nojs_class();

// // jupiterx_get_custom_footer();




// jupiterx_open_markup_e( 'jupiterx_post', 'article', [ 'class' => 'jupiterx-no-article jupiterx-post' ] );

// 	jupiterx_open_markup_e( 'jupiterx_post_header', 'header' );

// 		jupiterx_open_markup_e( 'jupiterx_post_title', 'h1', array( 'class' => 'jupiterx-post-title' ) );

// 			jupiterx_output_e( 'jupiterx_no_post_article_title_text', esc_html__( 'Whoops, no result found!', 'jupiterx' ) );

// 		jupiterx_close_markup_e( 'jupiterx_post_title', 'h1' );

// 	jupiterx_close_markup_e( 'jupiterx_post_header', 'header' );

// 	jupiterx_open_markup_e( 'jupiterx_post_body', 'div' );

// 		jupiterx_open_markup_e( 'jupiterx_post_content', 'div', array( 'class' => 'jupiterx-post-content' ) );

// 			jupiterx_open_markup_e( 'jupiterx_no_post_article_content', 'p' );

// 				jupiterx_output_e( 'jupiterx_no_post_article_content_text', esc_html__( 'It looks like nothing was found at this location. Try a new search?', 'jupiterx' ) );

// 			jupiterx_close_markup_e( 'jupiterx_no_post_article_content', 'p' );

// 				jupiterx_output_e( 'jupiterx_no_post_search_form', get_search_form( false ) );

// 		jupiterx_close_markup_e( 'jupiterx_post_content', 'div' );

// 	jupiterx_close_markup_e( 'jupiterx_post_body', 'div' );

// jupiterx_close_markup_e( 'jupiterx_post', 'article' );

?>

<?php
// var_dump("cccccccccc");
get_sidebar();
// var_dump("bbbbbbbbbb");
// get_footer();
?>

<style type="text/css">
.jupiterx-main-header {
	display: none !important;
}

.jupiterx-main-content {
	padding: 0px;
}

.jupiterx-main > .jupiterx-main-content > .container {
	max-width: 100% !important;
}

</style>
<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/jquery-ui.min.js" integrity="sha512-57oZ/vW8ANMjR/KQ6Be9v/+/h6bq9/l3f0Oc7vn6qMqyhvPd1cvKBRWWpzu0QoneImqr2SkmO4MSqU+RpHom3Q==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/themes/base/jquery-ui.min.css" integrity="sha512-ELV+xyi8IhEApPS/pSj66+Jiw+sOT1Mqkzlh8ExXihe4zfqbWkxPRi8wptXIO9g73FSlhmquFlUOuMSoXz5IRw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/themes/base/theme.min.css" integrity="sha512-hbs/7O+vqWZS49DulqH1n2lVtu63t3c3MTAn0oYMINS5aT8eIAbJGDXgLt6IxDHcWyzVTgf9XyzZ9iWyVQ7mCQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<link rel='stylesheet' id='jte-style-css' href='/malaysia/wp-content/plugins/elementor-addon/css/jte.css' type='text/css' media='all' /> -->

<?php 
	// var_dump("aaaaaaaaa");
	// Get URL
	if(isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on')   
         $url = "https://";   
    else  
         $url = "http://";   
    // Append the host(domain name, ip) to the URL.   
    $url.= $_SERVER['HTTP_HOST'];   
    
    // Append the requested resource location to the URL   
    $url.= $_SERVER['REQUEST_URI'];    
      
    // echo $url; 
    // echo '<br/>';

    $meta_query = array('relation' => 'AND');
    $meta_query_search = array('relation' => 'OR');
    $meta_query_visibility = array('relation' => 'OR');
    $meta_query_hide_listing = array('relation' => 'OR');
    $hospital = '';
    $search = '';

    if (isset($_GET['jhospital'])) {
	  	// var_dump($_GET['jhospital']);
    	$hospital = sanitize_text_field($_GET['jhospital']); //pasrse the parameter 'state' to state variable
    	// echo $_GET['state'];
    	// echo '<br/>';
    	if ($hospital != 'all') {
    		// Debug: Log hospital filter
    		error_log("DEBUG FILTER: Filtering by hospital: " . $hospital);

			// Set up the query arguments to get hospital ID
			$jtempargs = array(
			    'post_type' => 'hospital',
			    'post_status' => 'publish',
			    'numberposts' => 1,
			    'title' => $hospital,
			    'fields' => 'ids'
			);

			// Get the hospital post
			$jtemppost = get_posts($jtempargs);

			// Check if the hospital post exists
			if ($jtemppost) {
				$jtemppost_excerpt = get_the_excerpt($jtemppost[0]);
				error_log("DEBUG FILTER: Found hospital ID: " . $jtemppost_excerpt . " for hospital: " . $hospital);

				// Create hospital filter with OR relation for multiple conditions
				$meta_query_hospital = array(
					'relation' => 'OR',
					array(
			      		'key' => 'hospital',
			      		'value' => $hospital,
			      		'compare' => 'LIKE'
			      	),
					array(
			            'key' => 'appointment_list',
			            'value' => $jtemppost_excerpt . '-',
			            'compare' => 'LIKE'
			        )
				);
		        $meta_query[] = $meta_query_hospital;
			} else {
				error_log("DEBUG FILTER: Hospital not found: " . $hospital);
			}

		}
	    // print_r('$meta_query: ');
      	// print_r($meta_query);
      	// echo '<br/>';
      	
	} 
	else {
	  	$hospital = '';
	}

	if (isset($_GET['jspecialty'])) {
	  	
    	$specialty = sanitize_text_field($_GET['jspecialty']); //pasrse the parameter 'state' to state variable
    	// echo $_GET['state'];
    	// echo '<br/>';
    	if ($specialty != 'all') {
    		// // check Specialty field if got tag
    		// $jtest2 = $specialty;
		    // $meta_query[] =  array(
	     //  		'key' => 'specialty',
	     //  		'value' => serialize($jtest2),
	     //        'compare' => 'LIKE',
	     //  	);

		    // check Specialtydetails field if got match keywords

	      	//$tempdata = get_post($specialty);
	      	//var_dump($tempdata->post_title);
	        // $meta_query[] = array(
	        //     'key' => 'specialtydetails', // Replace with the actual custom field key for company
	        //     'value' => $tempdata->post_title,
	        //     'compare' => 'LIKE',
	        // );
	        $meta_query[] = array(
	            'key' => 'specialty', // Replace with the actual custom field key for company
	            'value' => '"' . $specialty . '"',
	            'compare' => 'LIKE',
	        );
		}
	    // print_r('$meta_query: ');
      	// print_r($meta_query);
      	// echo '<br/>';
	} 
	else {
	  	$specialty = '';
	}

	// if (isset($_GET['search'])) {
    // 	$search = $_GET['search']; //pasrse the parameter 'state' to state variable
    // 	// echo $_GET['search'];
    // 	// echo '<br/>';

	//     $meta_query[] =  array(
    //   		'key' => 'hospital_name',
    //   		'value' => $search,
    //   		'compare' => 'LIKE'
    //   	);
    //   	// print_r($meta_query);
	// } 
	// else {
	//   	$search = '';
	// }
	if (isset($_GET['jsearch'])) {
    	$search = sanitize_text_field( $_GET['jsearch'] );
    	$search = str_replace('’', "'", $search);
    	//var_dump($search);
    	// echo $_GET['search'];
    	// echo '<br/>';
    	// print_r($_GET['search']);

	    // $meta_query_search[] =  array(
     //  		'key' => 'title',
     //  		'value' => '',
     //  		'compare' => '!='
     //  	);
      	$meta_query_search[] =  array(
      		'key' => 'designation',
      		'value' => '',
      		'compare' => '!='
      	);
      	$meta_query_search[] =  array(
      		'key' => 'specialty_api',
      		'value' => '',
      		'compare' => '!='
      	);


      	$meta_query[] = $meta_query_search;
      	// print_r($meta_query);
      	// print_r($meta_query_search);
	} 
	else {
	  	$search = '';
	}

    // if(count($_GET)){ //Check if url contain paramater
    // 	$url_components = parse_url($url);
    // 	$state = $_GET['state']; //pasrse the parameter 'state' to state variable
    // 	// echo $_REQUEST['state'];
    // 	// echo '<br/>';

	//     $meta_query[] =  array(
    //   		'key' => 'state',
    //   		'value' => $state,
    //   	);
    // } else {
    // 	$state = '';
    // }

    $meta_query_hide_listing[] = array(
        'key' => 'show_in_listing', // Replace with the actual custom field key for company
        'value' => '0',
        'compare' => '!=',
        'type' => 'BINARY'
    );
    $meta_query[] = $meta_query_hide_listing;

    // Debug: Log final meta_query
    error_log("DEBUG FILTER: Final meta_query: " . print_r($meta_query, true));

    $userAgent = $_SERVER['HTTP_USER_AGENT'];

	function isMobile($userAgent) {
	    return preg_match('/Mobile|Android|BlackBerry|iPhone|Windows Phone/', $userAgent) && !isTablet($userAgent);
	}

	function isTablet($userAgent) {
	    return preg_match('/Tablet|iPad|Nexus 7/', $userAgent);
	}

	function isDesktop($userAgent) {
	    return !isMobile($userAgent) && !isTablet($userAgent);
	}
?>

<!-- <div class="jteGeneralHeaderContainer">
	<div class="jteGeneralHeaderImgDesktop" style="background-image: url(/malaysia/wp-content/uploads/2023/11/doctor-banner-d-scaled.jpg);"></div>
	<div class="jteGeneralHeaderImgMobile" style="background-image: url(/malaysia/wp-content/uploads/2023/10/hospital-listing-banner-m.jpg);"></div>
	<div class="jteGeneralHeaderContent">
		<h1>Our <b>Doctor</b></h1>
		<p class="jteGeneralHeaderContentDesc">Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet.</p>
	</div>
</div> -->

<?php
	echo do_shortcode('[elementor-template id="14496"]');
?>




<div class="jteHospitalListingContentContainer jteDoctorListingContentContainer" id="doctors">

	<!-- <div class="jteDoctorListingSubdesc">
		<p>Lorem ipsum dolor sit amet. Ex enim quisquam ut porro modi sed quae alias! Aut repellendus amet ut enim asperiores sed maiores exercitationem aut excepturi maxime. Aut voluptates explicabo sit impedit tempore et illo fugiat eos dolorem optio est ipsam repellat aut alias doloremque!</p>
	</div> -->
	<?php
		echo do_shortcode('[elementor-template id="15508"]');
	?>

	<?php
		// print_r($meta_query);
		$page = ( get_query_var('paged') ) ? intval( get_query_var('paged') ) : 1;
		$posts_per_page = 9;

		if (isMobile($userAgent)) {
			$args = array(
	            'post_type' => 'doctor',
	            'orderby' => 'title',
	            'order' => 'ASC',
	            'posts_per_page' => $posts_per_page,
	            //'posts_per_page' => -1,
	            'post_status' => array('publish'),
	            // 'meta_query' => array(
	            //   	array(
	            //   		'key' => 'state',
	            //   		'value' => $state,
	            //   	)
	            // ),
	            'meta_query' => $meta_query,
	            'paged' => 1,
	            's' => $search,
	        );
		}
		elseif (isDesktop($userAgent) || isTablet($userAgent)) {
			$args = array(
	            'post_type' => 'doctor',
	            'orderby' => 'title',
	            'order' => 'ASC',
	            'posts_per_page' => $posts_per_page,
	            //'posts_per_page' => -1,
	            'post_status' => array('publish'),
	            // 'meta_query' => array(
	            //   	array(
	            //   		'key' => 'state',
	            //   		'value' => $state,
	            //   	)
	            // ),
	            'meta_query' => $meta_query,
	            'paged' => $page,
	            's' => $search,
	        );
		}

        // $args = array(
        //     'post_type' => 'doctor',
        //     'orderby' => 'title',
        //     'order' => 'ASC',
        //     'posts_per_page' => $posts_per_page,
        //     //'posts_per_page' => -1,
        //     'post_status' => array('publish'),
        //     // 'meta_query' => array(
        //     //   	array(
        //     //   		'key' => 'state',
        //     //   		'value' => $state,
        //     //   	)
        //     // ),
        //     'meta_query' => $meta_query,
        //     'paged' => $page,
        //     's' => $search,
        // );

		// Old
        // $args_hospital = array(
        //     'post_type'      => 'hospital',
        //     'post_status'    => 'publish',
        //     'posts_per_page' => -1,
        //     'orderby' => 'title',
        //     'order' => 'ASC',
        //     'meta_query' => array(
        //   		array(
        //       		'key' => 'show_in_filter', // Replace with the actual custom field key for company
		// 	     'value' => '0',
		// 	     'compare' => '!=',
		// 	     'type' => 'BINARY'
        //       	)
        //      ),
        // );

        // $hospital_list = get_posts($args_hospital);

		// Old
        // $args_specialty = array(
        //     'post_type'      => 'specialty',
        //     'post_status'    => 'publish',
        //     'posts_per_page' => -1,
        //     'orderby' => 'title',
        //     'order' => 'ASC'
        // );

        // $specialty_list = get_posts($args_specialty);
        

        $query = new WP_Query($args);

        // Debug: Log query results
        error_log("DEBUG FILTER: Query found " . $query->found_posts . " doctors");
        error_log("DEBUG FILTER: Query args: " . print_r($args, true));

  //       $hospitalArgs = array(
  //           'post_type' => 'hospital',
  //           'orderby' => 'title',
  //           'order' => 'ASC',
  //           'posts_per_page' => -1,
  //           'post_status' => array('publish'),
  //           'meta_query' => array(
  //         		array(
  //             		'key' => 'show_in_filter', // Replace with the actual custom field key for company
		// 	     'value' => '0',
		// 	     'compare' => '!=',
		// 	     'type' => 'BINARY'
  //             	)
  //            ),
  //       );

		// $hospitalQuery = new WP_Query($hospitalArgs);
		//var_dump($hospitalQuery["post_content"]);
        // print_r($meta_query);
  //       echo "<pre>";
		// print_r($query->request);
		// echo "</pre>";

        // $args2 = array(
        //     'post_type' => 'doctor',
        //     'orderby' => 'title',
        //     'order' => 'ASC',
        //     // 'posts_per_page' => 9,
        //     'posts_per_page' => -1,
        //     'post_status' => array('publish'),
        //     // 'paged' => $page,
        // );
        // $query2 = new WP_Query($args2);
        // print_r($query2);

  //       // get whole list of select option
  //       $field = get_field_object('state');
		// $choices = $field['choices'];
		// // print_r($choices);


		echo '<div class="jteDoctorListingContentFilter">';
			echo '<div class="">';
			echo '<select name="jhospital-list" id="jhospital-list">';
				echo '<option value="all">All Hospital</option>';

				// foreach ($hospital_list as $post) { 

		        // 	$custom_fields = get_fields($post->ID);
		        // 	$jselected = ($hospital == $custom_fields["hospital_name"]) ? "selected" : ""; 
		        // 	echo '<option value="'. $custom_fields["hospital_name"] .'" ' . $jselected . ' >'. $custom_fields["hospital_name"] .'</option>';
		        // }

		        $hospitalArgs = array(
		            'post_type' => 'hospital',
		            'orderby' => 'title',
		            'order' => 'ASC',
		            'posts_per_page' => -1,
		            'post_status' => array('publish'),
		            'meta_query' => array(
	              		array(
		              		'key' => 'show_in_filter', // Replace with the actual custom field key for company
					     'value' => '0',
					     'compare' => '!=',
					     'type' => 'BINARY'
		              	)
		             ),
		        );

				$hospitalQuery = new WP_Query($hospitalArgs);
				if ($hospitalQuery->have_posts()) {
			        while ($hospitalQuery->have_posts()) {
			            $hospitalQuery->the_post();
			            //var_dump($hospitalQuery);

			            $jselected = ($hospital == get_the_title($hospitalQuery->ID)) ? "selected" : "";
			            echo '<option value="'. get_the_title($hospitalQuery->ID) .'" ' . $jselected . '>' . get_the_title($hospitalQuery->ID) . '</option>';
			        }
			        wp_reset_postdata();
	    		}
			
			
			echo '</select>';
			echo '</div>';

			echo '<div class="">';
			echo '<select name="jspecialty-list" id="jspecialty-list">';
				echo '<option value="all">All Specialities</option>';

				// foreach ($specialty_list as $post) { 
		        // 	$custom_fields = get_fields($post->ID);
		        // 	$jselected = ($specialty == $post->ID) ? "selected" : "";
		        // 	echo '<option value="'. $post->ID .'" ' . $jselected . ' >'. $post->post_title .'</option>';
		        // }

		        $specialtiesArgs2 = array(
		            'post_type' => 'specialty',
		            'orderby' => 'title',
		            'order' => 'ASC',
		            'posts_per_page' => -1,
		            'fields' => 'ids',
		            'post_status' => array('publish')
		        );

				$specialtiesQuery2 = new WP_Query($specialtiesArgs2);

				if ($specialtiesQuery2->have_posts()) {
				    $post_ids = $specialtiesQuery2->posts;
				    foreach ($post_ids as $post_id) {
				        $jselected = ($specialty == $post_id) ? "selected" : "";
				        echo '<option value="'. $post_id .'" ' . $jselected . '>' . get_the_title($post_id) . '</option>';
				    }
				    wp_reset_postdata();
				}

				// if ($specialtiesQuery2->have_posts()) {
			    //     while ($specialtiesQuery2->have_posts()) {
			    //         $specialtiesQuery2->the_post();
			    //         // print_r($specialtiesQuery2);
			    //         // print_r($specialtiesQuery2->ID);

			    //         echo '<option value="'. $specialtiesQuery2->ID .'">' . get_the_title($specialtiesQuery2->ID) . '</option>';
			    //     }
	    		// }
			
			
			echo '</select>';
			echo '</div>';

			echo '<div class="jteDoctorListingContentFilterSearch CAHsearchField">';
			echo '<a href="javascript:void(0)" class="CAHclearBtn">x</a>';
			echo '<span class="jteDoctorListingContentFilterSearch-icon"></span>'; 
			echo '<input id="jsearchBox" type="text" name="jsearchBox" placeholder="Search Doctor\'s Name" value="'.$search.'">';
			echo '</div>';
			
		echo '</div>';

		echo '<div class="jteHospitalListingContent jteDoctorListingContent" >';
		if ($query->have_posts()) {

			// Doctor Name Change Case
			// function customTitleCase($string) {
			//     // Words that should be fully uppercase
			//     $uppercase_words = ['A/P', 'A/L'];

			//     // Split the string into words
			//     $words = explode(' ', $string);

			//     // Iterate through each word and convert accordingly
			//     foreach ($words as &$word) {
			//         // Check if the word should be fully uppercase
			//         if (in_array(strtoupper($word), $uppercase_words)) {
			//             $word = strtoupper($word);
			//         } else {
			//             // Convert to title case
			//             $word = ucfirst(strtolower($word));
			//         }
			//     }

			//     // Join the words back into a single string
			//     return implode(' ', $words);
			// }
	
	        while ($query->have_posts()) {
	          $query->the_post();
	          	// print_r($query);
	          	$thumbnail_id = get_post_thumbnail_id();
        		$full_size_image_url = wp_get_attachment_image_src($thumbnail_id, 'full');
			    // print_r($img);

			    //$stateField = get_field('state');
			    // print_r($stateField);

			    $doctor_name = get_the_title();
			    $doctor_name_final = ucwords(strtolower($doctor_name));
			    $doctor_name_final02 = customTitleCaseDoctor($doctor_name);

			    $doctor_url = "javascript:void(0);";
			    $doctor_class = "";
			    $true_false_value = get_field("show_inner");

			    // Check if the field value is not empty
		        if ($true_false_value !== null) {
		            // Output the field value
		            if ($true_false_value) {
		                $doctor_url = get_permalink();
			    		$doctor_class = "jshowinner";
		            } else {
		                //$tempshowinner = 'The true/false field "your_true_false_field_name" for the current post is: false';
		                $doctor_class = "jhideinner";
		            }
		        } else {
		            $doctor_url = get_permalink();
			    	$doctor_class = "jshowinner";
		        }

		        $hide_in_listing = get_field('hide_in_listing');

			    $appointment_arr = array();
	            $jdoctorid = get_field('doctorid');
	            $jdoctorname = $doctor_name;
	            $jhospital_id = 0;
	            $jhospital_name = "";
	            $location = get_field("hospital");
	            $tempappointment = get_field("appointment_list");
	            $appointment_url = get_field("aappointment_url");
				$commaSeparated = explode(", ", $tempappointment);

				$show_inner = get_field('show_inner');

				// Initialize appointment array
				$appointment_arr = array();

				// $location02 = get_field("hospital", false, false); // returned only ID
				// // print_r($location02);
				// if ($location02) {
				//     foreach ($location02 as $post_id) {
				//         $title = get_the_title($post_id);
				//         $url = get_post_permalink($post_id);
				//         echo $title;
				//         echo $url;
				//     }
				// }

				$resultArray = array();
				if(!empty($tempappointment)){
					// Debug: Log appointment_list data for Batu Kawan debugging
					if (strpos($jdoctorname, 'Dr') !== false) {
						error_log("DEBUG: Doctor: " . $jdoctorname . " | appointment_list: " . $tempappointment);
					}
					foreach ($commaSeparated as $element) {
						if (strpos($element, '-') !== false) {
							list($jhospital_id, $status) = explode("-", $element);
						    $temphospitalname = "";
						    // Debug: Log each hospital-status pair
						    error_log("DEBUG: Doctor: " . $jdoctorname . " | Hospital ID: " . $jhospital_id . " | Status: " . $status);
						    
						    // foreach ($location as $post_object) {
						    // 	//jtesttest = get_field()
						    // 	echo "<pre>" . $jhospital_id . " ||| " . $post_object->post_excerpt . "</pre>";
						    // 	if( (int)trim($jhospital_id) == (int)trim($post_object->post_excerpt)){
						    // 		$temphospitalname = $post_object->post_title;
						    // 	}
						    // }

						    if (is_array($location)) {
				                // It's an array (could be a repeater, relationship, etc.)
				                foreach ($location as $post_object) {
				                    if( (int)trim($jhospital_id) == (int)trim($post_object->post_excerpt)){
							    		$temphospitalname = $post_object->post_title;
							    		// Debug: Log hospital name matching
							    		error_log("DEBUG: Doctor: " . $jdoctorname . " | Matched Hospital: " . $temphospitalname . " | Hospital ID: " . $jhospital_id);
							    	}
				                }
				            } elseif (is_object($location) && $location instanceof WP_Post) {
				                $jhospital_id = get_the_excerpt($post_object->ID);
				                $jhospital_name = get_the_title($location->ID);

				                if( (int)trim($jhospital_id) == (int)trim($post_object->post_excerpt)){
						    		$temphospitalname = $post_object->post_title;
						    	}
				                // It's a post object
				            } else {
				                $jhospital_id = get_the_excerpt($post_object->ID);
				                $jhospital_name = get_the_title($location->ID);

				                if( (int)trim($jhospital_id) == (int)trim($post_object->post_excerpt)){
						    		$temphospitalname = $post_object->post_title;
						    	}
				                // It's a simple data type
				            }

						    $resultArray[] = array("hospitalid" => $jhospital_id, "status" => $status, "hospitalname" => $temphospitalname);

						    // print_r($status);
						    // Debug: Log appointment button creation logic
						    error_log("DEBUG: Doctor: " . $jdoctorname . " | Status: " . $status . " | Hospital: " . $temphospitalname . " | Creating appointment: " . ($status == 2 && $temphospitalname != "" ? "YES" : "NO"));

						    if($status == 2 && $temphospitalname != ""){
						    	// Check if hospital is Batu Kawan for special URL
						    	if($temphospitalname == "Batu Kawan") {
						    		$appointment_arr[] = "https://www.columbiaasia.com/malaysia/hospitals/batu-kawan#batu-kawan-maa";
						    		error_log("DEBUG: Added Batu Kawan appointment URL for: " . $jdoctorname);
						    	} else {
						    	$appointment_arr[] = "https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/%3FphyID=".$jdoctorid."&searchPhyAndSpec=".str_replace('/', '%252F', $jdoctorname)."&location=".$jhospital_id."&searchLocation=".$temphospitalname."";
						    	error_log("DEBUG: Added standard appointment URL for: " . $jdoctorname . " at " . $temphospitalname);
						    }
						}
							else{
								
							}

						    // Special case: If page is filtered by "Batu Kawan" hospital and doctor has status=2 but no hospital match above
						    if($hospital == "Batu Kawan" && $status == 2 && $temphospitalname != "Batu Kawan") {
						    	$appointment_arr[] = "https://www.columbiaasia.com/malaysia/hospitals/batu-kawan#batu-kawan-maa";
						    	error_log("DEBUG: Added Batu Kawan special case appointment URL for: " . $jdoctorname);
						    }
						}
					    
					}
				}
				
				//var_dump($resultArray);
				// //var_dump($resultArray);
	      //       if (is_array($location)) {
	      //           // It's an array (could be a repeater, relationship, etc.)
	      //           foreach ($location as $post_object) {
	      //               //print_r($post_object);
	      //               $jhospital_id = get_the_excerpt($post_object->ID);
	      //               $jhospital_name = get_the_title($post_object->ID);

	      //               $appointment_arr[] = "https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/%3FphyID=".$jdoctorid."&searchPhyAndSpec=".$jdoctorname."&location=".$jhospital_id."&searchLocation=".$jhospital_name."";
	      //               // // Process each item in the array
	      //               // if (is_object($item) && $item instanceof WP_Post) {
	      //               //     // It's a post object within the array
	      //               // } else {
	      //               //     // It's a simple data type within the array
	      //               // }
	      //           }
	      //       } elseif (is_object($location) && $location instanceof WP_Post) {
	      //           $jhospital_id = get_the_excerpt($post_object->ID);
	      //           $jhospital_name = get_the_title($location->ID);

	      //           $appointment_arr[] = "https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/%3FphyID=".$jdoctorid."&searchPhyAndSpec=".$jdoctorname."&location=".$jhospital_id."&searchLocation=".$jhospital_name."";
	      //           // It's a post object
	      //       } else {
	      //           $jhospital_id = get_the_excerpt($post_object->ID);
	      //           $jhospital_name = get_the_title($location->ID);

	      //           $appointment_arr[] = "https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/%3FphyID=".$jdoctorid."&searchPhyAndSpec=".$jdoctorname."&location=".$jhospital_id."&searchLocation=".$jhospital_name."";
	      //           // It's a simple data type
	      //       }
	            
	      //       $appointment_url = "https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/%3FphyID=".$jdoctorid."&searchPhyAndSpec=".$jdoctorname."&location=".$jhospital_id."&searchLocation=".$jhospital_name."";
			    // print_r($doctor_name_final);

			    // print_r($appointment_arr);
	        ?>

	          
	        <div class="jteHospitalListingContentCol <?=$doctor_class?>">
	          	<a href="<?=$doctor_url ?>" class="jteHospitalListingContentColImg " style="background-image: url(<?php echo $full_size_image_url[0] ?>)"></a>
	          	<div class="jteHospitalListingContentColInner">
	          		<div class="jteHospitalListingContentColTitle">
	          			<a href="<?=$doctor_url ?>" class="jteHospitalListingContentColTitleInner">
	          				<h3><?php 
	          					// echo $doctor_name_final; 
	          					echo $doctor_name_final02; 
	          				?></h3>
	          			</a>
	          		</div>
	          		<div class="jteHospitalListingContentColTxt">
	          			<div class="jteHospitalListingContentColTxtList">
		          			<p><?php echo get_field('specialty_api'); ?></p>
		          		</div>
	          			<div class="jteHospitalListingContentColTxtList2">
		          			<p><?php echo get_field('designation'); ?></p>
		          		</div>
		          		<?php
		          		if($location){
                			echo '<div class="jteDoctorCarouselListHospital"><i></i><div>';

	                			// new code check data
	                			if (is_array($location)) {
								    // It's an array (could be a repeater, relationship, etc.)
								    $index = 0;
	                				$count = count($location);
								    foreach ($location as $post2) {
								    	if ($index < $count - 1) {
									    	echo '<a href="'. get_post_permalink($post2->ID) .'">';
			                					echo ''. get_the_title($post2->ID) .', ';
			                				echo '</a>';
			                			}else{
			                				echo '<a href="'. get_post_permalink($post2->ID) .'">';
			                					echo ''. get_the_title($post2->ID);
			                				echo '</a>';
			                			}
			                			$index++;
								    }
								} elseif (is_object($location) && $location instanceof WP_Post) {
									echo '<a href="'. get_post_permalink($hospital->ID) .'">';
	                					echo get_the_title($hospital->ID);
	                				echo '</a>';
								    // It's a post object
								} else {
									echo '<a href="'. get_post_permalink($location->ID) .'">';
	                					echo get_the_title($location->ID);
	                				echo '</a>';
								    // It's a simple data type
								}
							echo '</div></div>';
							}
						?>
		          		

	          		</div>	

	          		<?php
					// Debug: Log final appointment array
					error_log("DEBUG: Doctor: " . $jdoctorname . " | Final appointment_arr: " . print_r($appointment_arr, true));

					// Ensure $appointment_arr is set and is an array
					if (!empty($appointment_arr) && is_array($appointment_arr)) {
						// Remove any empty values to prevent empty buttons
						$appointment_arr = array_filter($appointment_arr);

						if (!empty($appointment_arr)) {
							$appointment_str = implode("|||", $appointment_arr);
							error_log("DEBUG: Doctor: " . $jdoctorname . " | Final appointment_str: " . $appointment_str);

							if (strpos($appointment_str, "|||") !== false) {
								// Case where multiple appointments exist (Popup)
								echo '<div class="jteHospitalListingContentColTxtBtn">';
								echo '<a href="javascript:void(0)" data-href="'. $appointment_str .'" class="doctorPopup">Make An Appointment</a>';
								echo '</div>';
						} else {
								// Case where a single valid appointment link exists
								echo '<div class="jteHospitalListingContentColTxtBtn">';
								// Check if this is a Batu Kawan URL or if page is filtered by Batu Kawan
								if (strpos($appointment_str, 'batu-kawan') !== false || $hospital == "Batu Kawan") {
									echo '<a href="'. $appointment_str .'" class="raven-button appointment-btn" data-doctor="' . esc_attr($jdoctorname) . '" title="Doctor: ' . esc_attr($jdoctorname) . '">Make An Appointment</a>';
								} else {
									echo '<a href="'. $appointment_str .'" target="_blank">Make An Appointment</a>';
								}
								echo '</div>';
							}
						}
					}
				?>



	          		
	          	</div>
	            
	        </div>

	    <?php  }
		  	wp_reset_postdata();
		  	echo '</div>';
		} else {
			$noPost = "Sorry, no doctors found. Perhaps you'd like to explore other hospitals/specialties?";
			// No posts found, do something else
			// echo '<div class="jteHospitalListingContent" >';
				echo '<div class="jteHospitalListingContentNoPost"><p>'.$noPost.'</p></div>';
			// echo '</div>';
			echo '</div>';
		}
		$big = 999999999; // need an unlikely integer
		$paged = get_query_var('paged') ? get_query_var('paged') : 1;
		$max_num_pages = $query->max_num_pages;


		if (isMobile($userAgent)) {
    		echo '<div class="jteHAlistingPaginationLM" data-page="'. $paged .'" data-ppp="'. $posts_per_page .'" data-maxpage="'. $max_num_pages .'">';
    		if ($query->have_posts()) {
	    		echo '<a href="javascript:void(0)">Load More</a>';
	    	}
    		echo '</div>';
    	}
    	else {
    		echo '<div class="jteHAlistingPagination" data-page="'. $paged .'" data-ppp="'. $posts_per_page .'" data-maxpage="'. $max_num_pages .'">';

			if($max_num_pages > 1){
		        if ($paged >= 1) {
				    echo '<a class="prev" href="' . get_previous_posts_page_link($max_num_pages) . '">Previous</a>';
				}
		        echo paginate_links( array(
					'base' => str_replace( $big, '%#%', html_entity_decode( get_pagenum_link( $big ) ) ),
					'format' => '?paged=%#%',
					'current' => max( 1, get_query_var('paged') ),
					'total' => $query->max_num_pages,
					'prev_text' => '<',
				    'next_text' => '>',
				    'type' => 'list',
				));
				if ($paged <= $max_num_pages) {
				    echo '<a class="next" href="' . get_next_posts_page_link($max_num_pages) . '">Next</a>';
				}

				if(get_query_var('paged') < 1) {
        			$pageNumber = 1;
        		} else {
        			$pageNumber = get_query_var('paged');
        		}

        		echo '<div class="jteHAlistingPaginationNumber">Go to <input type="number" value="'.$pageNumber.'" min="1"></div>';
			}

			echo '</div>';
    	}


		
	    ?>
	
</div>

<script>
    document.querySelector('.jteDoctorListingContentFilterSearch-icon').addEventListener('click', function() {
        const searchValue = document.getElementById('jsearchBox').value.trim();
        
        if (searchValue) {
            // Perform the search functionality here
            console.log("Searching for: " + searchValue);                   
            
            // Example of triggering a search or form submission
            // You can replace this with actual search functionality
            window.location.href = '/malaysia/doctors/?jsearch=' + encodeURIComponent(searchValue);
        } else {
            alert('Please enter a doctor\'s name.');
        }
    });

    // Add jQuery UI selectmenu and change handlers for doctor listing filters
    $(document).ready(function() {
        // Initialize selectmenu for hospital and specialty dropdowns
        $("#jhospital-list").selectmenu();
        $("#jspecialty-list").selectmenu();

        // Hospital filter change handler
        $('#jhospital-list').on('selectmenuchange', function(event, ui) {
            console.log('Hospital filter changed to:', $(this).val());
            updateDoctorListingURL('jhospital', $(this).val());
        });

        // Specialty filter change handler
        $('#jspecialty-list').on('selectmenuchange', function(event, ui) {
            console.log('Specialty filter changed to:', $(this).val());
            updateDoctorListingURL('jspecialty', $(this).val());
        });

        // Function to update URL with filter parameters
        function updateDoctorListingURL(paramName, paramValue) {
            var baseUrl = window.location.href.split('?')[0];
            var currentParams = new URLSearchParams(window.location.search);

            if (paramValue === 'all') {
                currentParams.delete(paramName);
            } else {
                currentParams.set(paramName, paramValue);
            }

            // Reset to page 1 when filtering
            currentParams.delete('paged');

            var newUrl = baseUrl + (currentParams.toString() ? '?' + currentParams.toString() : '');

            // Redirect to the new URL
            window.location.href = newUrl;
        }

        // Function to fix appointment buttons based on status
        function fixAppointmentButtons() {
            console.log('Fixing appointment buttons...');

            // Get current URL parameters
            var urlParams = new URLSearchParams(window.location.search);
            var hospitalFilter = urlParams.get('jhospital');
            console.log('Current hospital filter:', hospitalFilter);

            $('.jteHospitalListingContentCol').each(function() {
                var $doctorCard = $(this);
                var $statusElement = $doctorCard.find('.jteHospitalListingContentColTxtStatus');
                var $appointmentBtn = $doctorCard.find('.jteHospitalListingContentColTxtBtn');
                var doctorName = $doctorCard.find('.jteHospitalListingContentColTitle h3').text().trim();

                console.log('Processing doctor:', doctorName, 'Has appointment btn:', $appointmentBtn.length > 0);

                if ($statusElement.length > 0) {
                    var statusText = $statusElement.text().trim();
                    var status = 0; // Default status

                    // Determine status based on text content
                    if (statusText.includes('Available')) {
                        status = 2;
                    } else if (statusText.includes('Busy') || statusText.includes('Not Available')) {
                        status = 1;
                    } else {
                        status = 0;
                    }

                    console.log('Doctor status:', status, 'Text:', statusText);

                    // Only show appointment button if status is 2
                    if (status === 2) {
                        $appointmentBtn.show();

                        // Check current URL and update if needed
                        var $link = $appointmentBtn.find('a');
                        if ($link.length > 0) {
                            var currentUrl = $link.attr('href');
                            console.log('Current appointment URL:', currentUrl);

                            // If hospital filter is "Batu Kawan", update the button
                            if (hospitalFilter === 'Batu Kawan') {
                                if ($link.length > 0 && doctorName) {
                                    var newUrl = 'https://www.columbiaasia.com/malaysia/hospitals/batu-kawan#batu-kawan-maa?doctor=' + encodeURIComponent(doctorName);
                                    $link.attr('href', newUrl);
                                    $link.addClass('raven-button appointment-btn');
                                    $link.attr('data-doctor', doctorName);
                                    $link.attr('title', 'Doctor: ' + doctorName);
                                    console.log('Updated Batu Kawan button for:', doctorName, 'New URL:', newUrl);
                                }
                            }
                        }
                    } else {
                        $appointmentBtn.hide();
                        console.log('Hiding appointment button for status:', status);
                    }
                }
            });
        }

        // Run the fix function on page load and after any dynamic content changes
        $(document).ready(function() {
            setTimeout(fixAppointmentButtons, 500); // Small delay to ensure content is loaded
        });

        // Also run after any AJAX content is loaded (if applicable)
        $(document).ajaxComplete(function() {
            setTimeout(fixAppointmentButtons, 100);
        });
    });
    </script>

<!-- <script type='text/javascript' src='/malaysia/wp-content/plugins/elementor-addon/js/jte.js'></script> -->




<?php
// get_sidebar();
// the_content();
get_footer();
?>



